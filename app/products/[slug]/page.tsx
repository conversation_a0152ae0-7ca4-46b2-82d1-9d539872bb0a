import { notFound } from "next/navigation"

import { ProductInfo } from "@/components/storefront/products/product-info"
import { RelatedProducts } from "@/components/related-products"
import { SizeRecommendation } from "@/components/size-recommendation"
import { AIProductRecommendations } from "@/components/storefront/products/ai-product-recommendations"

interface Props {
  params: {
    slug: string
  }
}

async function getProductBySlug(slug: string) {
  try {
    const response = await fetch(`/api/e-commerce/products/slug/${slug}`, {
      cache: 'no-store' // Ensure fresh data for each request
    })

    if (!response.ok) {
      return null
    }

    const result = await response.json()
    return result.success ? result.data : null
  } catch (error) {
    console.error('Error fetching product:', error)
    return null
  }
}

export default async function ProductPage({ params }: Props) {
  const product = await getProductBySlug(params.slug)

  if (!product) {
    notFound()
  }

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 pt-16 pb-24 sm:px-6 lg:max-w-7xl lg:px-8">
        <div className="lg:grid lg:grid-cols-2 lg:items-start lg:gap-x-8">
          {/* Image gallery */}
          <div className="flex flex-col">
            <div className="mx-auto w-full max-w-2xl sm:px-6 lg:max-w-7xl lg:px-8">
              <div className="aspect-w-3 aspect-h-4 rounded-lg overflow-hidden">
                <img
                  src={product.images?.[0]?.url || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
                  alt={product.title}
                  className="w-full h-full object-center object-cover"
                />
              </div>
            </div>
          </div>

          {/* Product info */}
          <div className="mt-10 px-4 sm:px-0 lg:mt-0">
            <div className="lg:col-span-5">
              <ProductInfo product={product} />
              <SizeRecommendation />
            </div>
          </div>
        </div>

        <RelatedProducts categoryId={product.categories?.[0]?.id || ''} currentProductId={product.id} />
        <AIProductRecommendations productId={product.id} />
      </div>
    </div>
  )
}
