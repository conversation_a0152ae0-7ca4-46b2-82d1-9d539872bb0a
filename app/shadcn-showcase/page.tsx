'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import {
  Sparkles,
  Code,
  Eye,
  Copy,
  CheckCircle,
  Palette,
  Layers,
  Grid,
  Menu,
  Database,
  Settings,
  Zap,
  AlertCircle,
  AlertTriangle
} from 'lucide-react'
import {
  DynamicAIBlock,
  type DynamicAIBlockData,
} from '@/lib/ai-block-generator'
// PageBuilderProvider no longer needed - using Zustand store

export default function ShadcnShowcasePage() {
  const [selectedCategory, setSelectedCategory] = useState('core')
  const [searchTerm, setSearchTerm] = useState('')
  const [copiedComponent, setCopiedComponent] = useState<string | null>(null)

  // Comprehensive Shadcn component examples with JSX
  const componentCategories = {
    core: {
      title: 'Core Components',
      icon: <Layers className="h-4 w-4" />,
      components: [
        {
          name: 'Button',
          description: 'Displays a button or a component that looks like a button.',
          jsx: `<div className="flex gap-2 flex-wrap">
  <Button>Default</Button>
  <Button variant="destructive">Destructive</Button>
  <Button variant="outline">Outline</Button>
  <Button variant="secondary">Secondary</Button>
  <Button variant="ghost">Ghost</Button>
  <Button variant="link">Link</Button>
  <Button size="sm">Small</Button>
  <Button size="lg">Large</Button>
  <Button disabled>Disabled</Button>
</div>`
        },
        {
          name: 'Card',
          description: 'Displays a card with header, content, and footer.',
          jsx: `<Card className="w-full max-w-md">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description goes here</CardDescription>
  </CardHeader>
  <CardContent>
    <p>This is the card content area where you can put any content.</p>
  </CardContent>
</Card>`
        },
        {
          name: 'Badge',
          description: 'Displays a badge or a component that looks like a badge.',
          jsx: `<div className="flex gap-2 flex-wrap">
  <Badge>Default</Badge>
  <Badge variant="secondary">Secondary</Badge>
  <Badge variant="destructive">Destructive</Badge>
  <Badge variant="outline">Outline</Badge>
</div>`
        },
        {
          name: 'Input & Label',
          description: 'Displays a form input field with label.',
          jsx: `<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter your email" />
  <Label htmlFor="password">Password</Label>
  <Input id="password" type="password" placeholder="Enter your password" />
</div>`
        },
        {
          name: 'Separator',
          description: 'Visually or semantically separates content.',
          jsx: `<div>
  <div className="space-y-1">
    <h4 className="text-sm font-medium leading-none">Radix Primitives</h4>
    <p className="text-sm text-muted-foreground">An open-source UI component library.</p>
  </div>
  <Separator className="my-4" />
  <div className="flex h-5 items-center space-x-4 text-sm">
    <div>Blog</div>
    <Separator orientation="vertical" />
    <div>Docs</div>
    <Separator orientation="vertical" />
    <div>Source</div>
  </div>
</div>`
        }
      ]
    },
    forms: {
      title: 'Form Components',
      icon: <Grid className="h-4 w-4" />,
      components: [
        {
          name: 'Checkbox',
          description: 'A control that allows the user to toggle between checked and not checked.',
          jsx: `<div className="flex items-center space-x-2">
  <Checkbox id="terms" />
  <Label htmlFor="terms">Accept terms and conditions</Label>
</div>`
        },
        {
          name: 'Radio Group',
          description: 'A set of checkable buttons—known as radio buttons—where no more than one can be checked at a time.',
          jsx: `<RadioGroup defaultValue="option-one">
  <div className="flex items-center space-x-2">
    <RadioGroupItem value="option-one" id="option-one" />
    <Label htmlFor="option-one">Option One</Label>
  </div>
  <div className="flex items-center space-x-2">
    <RadioGroupItem value="option-two" id="option-two" />
    <Label htmlFor="option-two">Option Two</Label>
  </div>
</RadioGroup>`
        },
        {
          name: 'Select',
          description: 'Displays a list of options for the user to pick from—triggered by a button.',
          jsx: `<Select>
  <SelectTrigger className="w-[180px]">
    <SelectValue placeholder="Select a fruit" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="apple">Apple</SelectItem>
    <SelectItem value="banana">Banana</SelectItem>
    <SelectItem value="orange">Orange</SelectItem>
  </SelectContent>
</Select>`
        },
        {
          name: 'Switch',
          description: 'A control that allows the user to toggle between checked and not checked.',
          jsx: `<div className="flex items-center space-x-2">
  <Switch id="airplane-mode" />
  <Label htmlFor="airplane-mode">Airplane Mode</Label>
</div>`
        },
        {
          name: 'Slider',
          description: 'An input where the user selects a value from within a given range.',
          jsx: `<div className="space-y-4">
  <Label>Volume</Label>
  <Slider defaultValue={[50]} max={100} step={1} className="w-full" />
</div>`
        }
      ]
    },
    navigation: {
      title: 'Navigation',
      icon: <Menu className="h-4 w-4" />,
      components: [
        {
          name: 'Tabs',
          description: 'A set of layered sections of content—known as tab panels—that are displayed one at a time.',
          jsx: `<Tabs defaultValue="account" className="w-[400px]">
  <TabsList>
    <TabsTrigger value="account">Account</TabsTrigger>
    <TabsTrigger value="password">Password</TabsTrigger>
  </TabsList>
  <TabsContent value="account">
    <p>Make changes to your account here.</p>
  </TabsContent>
  <TabsContent value="password">
    <p>Change your password here.</p>
  </TabsContent>
</Tabs>`
        },
        {
          name: 'Navigation Menu',
          description: 'A collection of links for navigating websites.',
          jsx: `<NavigationMenu>
  <NavigationMenuList>
    <NavigationMenuItem>
      <NavigationMenuTrigger>Getting started</NavigationMenuTrigger>
      <NavigationMenuContent>
        <div className="grid gap-3 p-6 md:w-[400px]">
          <NavigationMenuLink>Introduction</NavigationMenuLink>
          <NavigationMenuLink>Installation</NavigationMenuLink>
        </div>
      </NavigationMenuContent>
    </NavigationMenuItem>
  </NavigationMenuList>
</NavigationMenu>`
        }
      ]
    },
    display: {
      title: 'Display Components',
      icon: <Eye className="h-4 w-4" />,
      components: [
        {
          name: 'Avatar',
          description: 'An image element with a fallback for representing the user.',
          jsx: `<div className="flex gap-2">
  <Avatar>
    <AvatarImage src="https://github.com/shadcn.png" />
    <AvatarFallback>CN</AvatarFallback>
  </Avatar>
  <Avatar>
    <AvatarFallback>JD</AvatarFallback>
  </Avatar>
</div>`
        },
        {
          name: 'Alert',
          description: 'Displays a callout for user attention.',
          jsx: `<div className="space-y-4">
  <Alert>
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>Heads up!</AlertTitle>
    <AlertDescription>
      You can add components to your app using the cli.
    </AlertDescription>
  </Alert>
  <Alert variant="destructive">
    <AlertTriangle className="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>
      Your session has expired. Please log in again.
    </AlertDescription>
  </Alert>
</div>`
        },
        {
          name: 'Progress',
          description: 'Displays an indicator showing the completion progress of a task.',
          jsx: `<div className="space-y-4">
  <div>
    <Label>Progress: 33%</Label>
    <Progress value={33} className="w-full" />
  </div>
  <div>
    <Label>Progress: 66%</Label>
    <Progress value={66} className="w-full" />
  </div>
</div>`
        },
        {
          name: 'Skeleton',
          description: 'Use to show a placeholder while content is loading.',
          jsx: `<div className="flex items-center space-x-4">
  <Skeleton className="h-12 w-12 rounded-full" />
  <div className="space-y-2">
    <Skeleton className="h-4 w-[250px]" />
    <Skeleton className="h-4 w-[200px]" />
  </div>
</div>`
        }
      ]
    },
    overlays: {
      title: 'Overlay Components',
      icon: <Layers className="h-4 w-4" />,
      components: [
        {
          name: 'Dialog',
          description: 'A window overlaid on either the primary window or another dialog window.',
          jsx: `<Dialog>
  <DialogTrigger asChild>
    <Button variant="outline">Edit Profile</Button>
  </DialogTrigger>
  <DialogContent className="sm:max-w-[425px]">
    <DialogHeader>
      <DialogTitle>Edit profile</DialogTitle>
      <DialogDescription>
        Make changes to your profile here. Click save when you're done.
      </DialogDescription>
    </DialogHeader>
    <div className="grid gap-4 py-4">
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="name" className="text-right">Name</Label>
        <Input id="name" value="Pedro Duarte" className="col-span-3" />
      </div>
    </div>
  </DialogContent>
</Dialog>`
        },
        {
          name: 'Popover',
          description: 'Displays rich content in a portal, triggered by a button.',
          jsx: `<Popover>
  <PopoverTrigger asChild>
    <Button variant="outline">Open popover</Button>
  </PopoverTrigger>
  <PopoverContent className="w-80">
    <div className="grid gap-4">
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Dimensions</h4>
        <p className="text-sm text-muted-foreground">
          Set the dimensions for the layer.
        </p>
      </div>
    </div>
  </PopoverContent>
</Popover>`
        },
        {
          name: 'Tooltip',
          description: 'A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.',
          jsx: `<TooltipProvider>
  <Tooltip>
    <TooltipTrigger asChild>
      <Button variant="outline">Hover me</Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>Add to library</p>
    </TooltipContent>
  </Tooltip>
</TooltipProvider>`
        }
      ]
    },
    data: {
      title: 'Data Display',
      icon: <Database className="h-4 w-4" />,
      components: [
        {
          name: 'Table',
          description: 'A responsive table component.',
          jsx: `<Table>
  <TableCaption>A list of your recent invoices.</TableCaption>
  <TableHeader>
    <TableRow>
      <TableHead className="w-[100px]">Invoice</TableHead>
      <TableHead>Status</TableHead>
      <TableHead>Method</TableHead>
      <TableHead className="text-right">Amount</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell className="font-medium">INV001</TableCell>
      <TableCell>Paid</TableCell>
      <TableCell>Credit Card</TableCell>
      <TableCell className="text-right">$250.00</TableCell>
    </TableRow>
    <TableRow>
      <TableCell className="font-medium">INV002</TableCell>
      <TableCell>Pending</TableCell>
      <TableCell>PayPal</TableCell>
      <TableCell className="text-right">$150.00</TableCell>
    </TableRow>
  </TableBody>
</Table>`
        }
      ]
    }
  }

  const copyToClipboard = async (jsx: string, componentName: string) => {
    try {
      await navigator.clipboard.writeText(jsx)
      setCopiedComponent(componentName)
      setTimeout(() => setCopiedComponent(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const filteredComponents = Object.entries(componentCategories).reduce((acc, [key, category]) => {
    const filtered = category.components.filter(component =>
      component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    if (filtered.length > 0) {
      acc[key] = { ...category, components: filtered }
    }
    return acc
  }, {} as typeof componentCategories)

  return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Palette className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Shadcn/UI Components Showcase
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Complete showcase of all Shadcn/UI components rendered dynamically with the AI Block Generator system.
            Copy JSX code and see live previews of every component.
          </p>
          <div className="flex items-center justify-center gap-2 mt-4">
            <Badge variant="secondary" className="text-sm">
              <Sparkles className="h-3 w-3 mr-1" />
              Dynamic Rendering
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Code className="h-3 w-3 mr-1" />
              Live JSX
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Eye className="h-3 w-3 mr-1" />
              Interactive Preview
            </Badge>
          </div>
        </div>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="Search components..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                <Settings className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
              <Button variant="outline" onClick={() => setSearchTerm('')}>
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Category Navigation */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-2">
              {Object.entries(componentCategories).map(([key, category]) => (
                <Button
                  key={key}
                  variant={selectedCategory === key ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(key)}
                  className="flex items-center gap-2"
                >
                  {category.icon}
                  {category.title}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Components Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {(searchTerm ? Object.entries(filteredComponents) : [[selectedCategory, componentCategories[selectedCategory as keyof typeof componentCategories]]]).map(([categoryKey, category]) => 
            category?.components.map((component, index) => {
              const blockData: DynamicAIBlockData = {
                id: `${categoryKey}-${index}`,
                type: 'shadcn-component',
                version: '1.0',
                configuration: {
                  componentName: component.name,
                  description: component.description,
                },
                jsx: component.jsx,
                aiGenerated: false,
                sandbox: false,
              }

              return (
                <Card key={`${categoryKey}-${component.name}`} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Zap className="h-5 w-5 text-primary" />
                          {component.name}
                        </CardTitle>
                        <CardDescription>{component.description}</CardDescription>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(component.jsx, component.name)}
                        className="flex items-center gap-2"
                      >
                        {copiedComponent === component.name ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                        {copiedComponent === component.name ? 'Copied!' : 'Copy JSX'}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <DynamicAIBlock
                      blockData={blockData}
                      enableCustomJSX={true}
                      enableLogicExecution={false}
                      enableEventHandling={true}
                      isolatedExecution={false}
                      showAIControls={true}
                      isEditing={false}
                      adaptiveRendering={true}
                    />
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-12 pt-8 border-t">
          <p className="text-muted-foreground">
            All Shadcn/UI components rendered dynamically with the AI Block Generator system.
          </p>
          <div className="flex items-center justify-center gap-4 mt-4">
            <Badge variant="outline">React 18</Badge>
            <Badge variant="outline">TypeScript</Badge>
            <Badge variant="outline">Tailwind CSS</Badge>
            <Badge variant="outline">Radix UI</Badge>
            <Badge variant="outline">Dynamic JSX</Badge>
          </div>
        </div>
      </div>
      </div>
  )
}
