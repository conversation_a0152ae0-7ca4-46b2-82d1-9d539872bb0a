// Customers API Routes
// GET /api/e-commerce/customers - Search and list customers
// POST /api/e-commerce/customers - Create a new customer

import { NextRequest, NextResponse } from 'next/server'
import { customerService, handleEcommerceError } from '@/lib/ecommerce'
import { CustomerSearchParams, CreateUserInput } from '@/lib/ecommerce/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const params: CustomerSearchParams = {
      query: searchParams.get('query') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      filters: {},
      sort: {
        field: (searchParams.get('sortBy') as any) || 'createdAt',
        direction: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
      }
    }

    // Parse filters
    if (searchParams.get('isActive')) {
      params.filters!.isActive = searchParams.get('isActive') === 'true'
    }

    if (searchParams.get('isBlocked')) {
      params.filters!.isBlocked = searchParams.get('isBlocked') === 'true'
    }

    if (searchParams.get('emailVerified')) {
      params.filters!.emailVerified = searchParams.get('emailVerified') === 'true'
    }

    if (searchParams.get('loyaltyTier')) {
      params.filters!.loyaltyTier = searchParams.get('loyaltyTier')!.split(',')
    }

    if (searchParams.get('totalSpentMin')) {
      params.filters!.totalSpentMin = parseFloat(searchParams.get('totalSpentMin')!)
    }

    if (searchParams.get('totalSpentMax')) {
      params.filters!.totalSpentMax = parseFloat(searchParams.get('totalSpentMax')!)
    }

    if (searchParams.get('orderCountMin')) {
      params.filters!.orderCountMin = parseInt(searchParams.get('orderCountMin')!)
    }

    if (searchParams.get('orderCountMax')) {
      params.filters!.orderCountMax = parseInt(searchParams.get('orderCountMax')!)
    }

    if (searchParams.get('createdAfter')) {
      params.filters!.createdAfter = new Date(searchParams.get('createdAfter')!)
    }

    if (searchParams.get('createdBefore')) {
      params.filters!.createdBefore = new Date(searchParams.get('createdBefore')!)
    }

    if (searchParams.get('lastOrderAfter')) {
      params.filters!.lastOrderAfter = new Date(searchParams.get('lastOrderAfter')!)
    }

    if (searchParams.get('lastOrderBefore')) {
      params.filters!.lastOrderBefore = new Date(searchParams.get('lastOrderBefore')!)
    }

    if (searchParams.get('tags')) {
      params.filters!.tags = searchParams.get('tags')!.split(',')
    }

    const result = await customerService().getCustomers(params)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          data: result.data?.data || [],
          pagination: result.data?.pagination
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Customers API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    const input: CreateUserInput = {
      email: body.email,
      firstName: body.firstName,
      lastName: body.lastName,
      displayName: body.displayName,
      phone: body.phone,
      dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : undefined,
      gender: body.gender,
      acceptsMarketing: body.acceptsMarketing,
      preferredLanguage: body.preferredLanguage,
      preferredCurrency: body.preferredCurrency,
      timezone: body.timezone,
      avatar: body.avatar,
      bio: body.bio,
      tags: body.tags,
      notes: body.notes,
      metafields: body.metafields
    }

    const result = await customerService.createCustomer(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create customer API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
