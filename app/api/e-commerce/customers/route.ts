// Production-Ready Customers API Routes
// GET /api/e-commerce/customers - Search and list customers
// POST /api/e-commerce/customers - Create a new customer

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const query = searchParams.get('query')
    const isActive = searchParams.get('isActive')
    const isBlocked = searchParams.get('isBlocked')
    const emailVerified = searchParams.get('emailVerified')
    const loyaltyTier = searchParams.get('loyaltyTier')
    const acceptsMarketing = searchParams.get('acceptsMarketing')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build where clause
    const where: any = {}

    if (query) {
      where.OR = [
        { email: { contains: query, mode: 'insensitive' } },
        { firstName: { contains: query, mode: 'insensitive' } },
        { lastName: { contains: query, mode: 'insensitive' } },
        { displayName: { contains: query, mode: 'insensitive' } },
        { phone: { contains: query, mode: 'insensitive' } }
      ]
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    if (isBlocked !== null) {
      where.isBlocked = isBlocked === 'true'
    }

    if (emailVerified !== null) {
      where.emailVerified = emailVerified === 'true'
    }

    if (loyaltyTier) {
      where.loyaltyTier = { in: loyaltyTier.split(',') }
    }

    if (acceptsMarketing !== null) {
      where.acceptsMarketing = acceptsMarketing === 'true'
    }

    // Build orderBy clause
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    // Get total count
    const total = await prisma.user.count({ where })

    // Get customers with pagination
    const customers = await prisma.user.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
        emailVerified: true,
        phoneVerified: true,
        lastLoginAt: true,
        acceptsMarketing: true,
        preferredLanguage: true,
        preferredCurrency: true,
        timezone: true,
        avatar: true,
        bio: true,
        isActive: true,
        isBlocked: true,
        customerSince: true,
        totalSpent: true,
        orderCount: true,
        averageOrderValue: true,
        lastOrderAt: true,
        loyaltyPoints: true,
        loyaltyTier: true,
        tags: true,
        notes: true,
        createdAt: true,
        updatedAt: true,
        orders: {
          select: {
            id: true,
            orderNumber: true,
            total: true,
            status: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        addresses: {
          select: {
            id: true,
            type: true,
            firstName: true,
            lastName: true,
            company: true,
            address1: true,
            address2: true,
            city: true,
            province: true,
            country: true,
            postalCode: true,
            phone: true,
            isDefault: true
          }
        }
      }
    })

    // Calculate pagination
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        data: customers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Customers API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    // Check if customer already exists
    const existingCustomer = await prisma.user.findUnique({
      where: { email: body.email }
    })

    if (existingCustomer) {
      return NextResponse.json(
        { success: false, error: 'Customer with this email already exists' },
        { status: 400 }
      )
    }

    // Create customer
    const customer = await prisma.user.create({
      data: {
        email: body.email,
        firstName: body.firstName || null,
        lastName: body.lastName || null,
        displayName: body.displayName || null,
        phone: body.phone || null,
        dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : null,
        gender: body.gender || null,
        emailVerified: body.emailVerified || false,
        phoneVerified: body.phoneVerified || false,
        acceptsMarketing: body.acceptsMarketing || false,
        preferredLanguage: body.preferredLanguage || 'en',
        preferredCurrency: body.preferredCurrency || 'ZAR',
        timezone: body.timezone || null,
        avatar: body.avatar || null,
        bio: body.bio || null,
        isActive: body.isActive !== undefined ? body.isActive : true,
        isBlocked: body.isBlocked || false,
        customerSince: new Date(),
        totalSpent: 0,
        orderCount: 0,
        averageOrderValue: 0,
        loyaltyPoints: body.loyaltyPoints || 0,
        loyaltyTier: body.loyaltyTier || null,
        metafields: body.metafields || null,
        tags: body.tags || [],
        notes: body.notes || null
      },
      include: {
        orders: {
          select: {
            id: true,
            orderNumber: true,
            total: true,
            status: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        addresses: true
      }
    })

    return NextResponse.json({
      success: true,
      data: customer
    }, { status: 201 })

  } catch (error) {
    console.error('Create customer API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}
