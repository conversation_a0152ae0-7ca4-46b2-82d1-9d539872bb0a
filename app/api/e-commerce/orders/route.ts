// Orders API Routes
// GET /api/e-commerce/orders - Search and list orders
// POST /api/e-commerce/orders - Create a new order

import { NextRequest, NextResponse } from 'next/server'
import { orderService, authService, handleEcommerceError } from '@/lib/ecommerce'
import { OrderSearchParams, CreateOrderInput } from '@/lib/ecommerce/types'

// Get service instances
const orderServiceInstance = orderService()
const authServiceInstance = authService()

export async function GET(request: NextRequest) {
  try {
    // Check if this is an admin request (no auth required for admin)
    const isAdminRequest = request.headers.get('x-admin-request') === 'true' ||
                          request.nextUrl.pathname.includes('/admin/')

    let currentUser = null
    let isAdmin = false

    if (!isAdminRequest) {
      // Get token from cookie or header for customer requests
      const token = request.cookies.get('auth-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')

      if (!token) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Get current user
      const userResult = await authServiceInstance.getCurrentUser(token)
      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      currentUser = userResult.data
      // Check if user is admin (you can implement your admin check logic here)
      isAdmin = currentUser.role === 'admin' || currentUser.isAdmin
    } else {
      // For admin requests, assume admin privileges
      isAdmin = true
    }

    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const params: OrderSearchParams = {
      query: searchParams.get('query') || undefined,
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      filters: {
        // Filter by current user's orders only (unless admin)
        customerId: isAdmin ? searchParams.get('customerId') || undefined : currentUser?.id,
        status: searchParams.get('status') ? searchParams.get('status')!.split(',') as any : undefined,
        paymentStatus: searchParams.get('paymentStatus') ? searchParams.get('paymentStatus')!.split(',') as any : undefined,
        fulfillmentStatus: searchParams.get('fulfillmentStatus') ? searchParams.get('fulfillmentStatus')!.split(',') as any : undefined,
        orderNumber: searchParams.get('orderNumber') || undefined,
        customerEmail: searchParams.get('customerEmail') || undefined,
        createdAfter: searchParams.get('createdAfter') ? new Date(searchParams.get('createdAfter')!) : undefined,
        createdBefore: searchParams.get('createdBefore') ? new Date(searchParams.get('createdBefore')!) : undefined,
        totalMin: searchParams.get('totalMin') ? parseFloat(searchParams.get('totalMin')!) : undefined,
        totalMax: searchParams.get('totalMax') ? parseFloat(searchParams.get('totalMax')!) : undefined
      },
      sort: {
        field: (searchParams.get('sortBy') as any) || 'createdAt',
        direction: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
      }
    }

    // Use getAllOrders method with filters
    const orders = await orderServiceInstance.getAllOrders({
      page: params.page,
      limit: params.limit,
      status: params.filters?.status?.[0],
      paymentStatus: params.filters?.paymentStatus?.[0]
    })

    // Calculate pagination (simplified)
    const total = orders.length
    const totalPages = Math.ceil(total / (params.limit || 20))

    return NextResponse.json({
      success: true,
      data: {
        data: orders,
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total,
          totalPages,
          hasNext: (params.page || 1) < totalPages,
          hasPrev: (params.page || 1) > 1
        }
      }
    })
  } catch (error) {
    console.error('Orders API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.customer?.email || !body.billingAddress || !body.shippingAddress || !body.items?.length) {
      return NextResponse.json(
        { success: false, error: 'Customer email, billing address, shipping address, and items are required' },
        { status: 400 }
      )
    }

    const input: CreateOrderInput = {
      customer: {
        id: body.customer.id,
        email: body.customer.email,
        firstName: body.customer.firstName,
        lastName: body.customer.lastName,
        phone: body.customer.phone
      },
      billingAddress: body.billingAddress,
      shippingAddress: body.shippingAddress,
      items: body.items.map((item: any) => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: parseInt(item.quantity),
        unitPrice: item.unitPrice ? {
          amount: parseFloat(item.unitPrice),
          currency: item.currency || 'ZAR'
        } : undefined,
        customAttributes: item.customAttributes,
        personalizedMessage: item.personalizedMessage,
        giftWrap: item.giftWrap || false
      })),
      shippingMethodId: body.shippingMethodId || 'standard',
      discountCodes: body.discountCodes,
      paymentMethodId: body.paymentMethodId,
      customerNote: body.customerNote,
      source: body.source || 'web',
      sourceIdentifier: body.sourceIdentifier,
      attributes: body.attributes,
      tags: body.tags
    }

    const result = await orderServiceInstance.createOrder(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create order API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
