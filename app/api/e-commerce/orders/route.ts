// Production-Ready Orders API Routes
// GET /api/e-commerce/orders - Search and list orders
// POST /api/e-commerce/orders - Create a new order

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Helper function to generate order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `ORD-${timestamp}-${random}`.toUpperCase()
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const query = searchParams.get('query')
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('paymentStatus')
    const fulfillmentStatus = searchParams.get('fulfillmentStatus')
    const customerEmail = searchParams.get('customerEmail')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build where clause
    const where: any = {}

    if (query) {
      where.OR = [
        { orderNumber: { contains: query, mode: 'insensitive' } },
        { customerEmail: { contains: query, mode: 'insensitive' } },
        { customerFirstName: { contains: query, mode: 'insensitive' } },
        { customerLastName: { contains: query, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = { in: status.split(',') }
    }

    if (paymentStatus) {
      where.paymentStatus = { in: paymentStatus.split(',') }
    }

    if (fulfillmentStatus) {
      where.fulfillmentStatus = { in: fulfillmentStatus.split(',') }
    }

    if (customerEmail) {
      where.customerEmail = { contains: customerEmail, mode: 'insensitive' }
    }

    // Build orderBy clause
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    // Get total count
    const total = await prisma.order.count({ where })

    // Get orders with pagination
    const orders = await prisma.order.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                title: true,
                slug: true
              }
            },
            variant: {
              select: {
                id: true,
                title: true,
                sku: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            displayName: true
          }
        },
        fulfillments: {
          select: {
            id: true,
            status: true,
            trackingNumber: true,
            trackingCompany: true,
            shippedAt: true,
            deliveredAt: true
          }
        },
        payments: {
          select: {
            id: true,
            status: true,
            amount: true,
            currency: true,
            gatewayId: true
          }
        }
      }
    })

    // Calculate pagination
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        data: orders,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Orders API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.customerEmail || !body.billingAddress || !body.shippingAddress || !body.items?.length) {
      return NextResponse.json(
        { success: false, error: 'Customer email, billing address, shipping address, and items are required' },
        { status: 400 }
      )
    }

    // Calculate totals
    let subtotal = 0
    const processedItems = []

    for (const item of body.items) {
      // Get product/variant details
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: {
          variants: item.variantId ? {
            where: { id: item.variantId }
          } : false,
          images: {
            orderBy: { position: 'asc' },
            take: 1
          }
        }
      })

      if (!product) {
        return NextResponse.json(
          { success: false, error: `Product ${item.productId} not found` },
          { status: 400 }
        )
      }

      const variant = item.variantId ? product.variants?.[0] : null
      const price = variant ? variant.price : product.price
      const title = variant ? `${product.title} - ${variant.title}` : product.title
      const sku = variant?.sku || product.handle

      const itemTotal = price.toNumber() * item.quantity
      subtotal += itemTotal

      processedItems.push({
        productId: item.productId,
        variantId: item.variantId || null,
        quantity: item.quantity,
        unitPrice: price,
        totalPrice: itemTotal,
        currency: 'ZAR',
        productTitle: title,
        productSlug: product.slug,
        productImage: product.images?.[0]?.url || null,
        variantTitle: variant?.title || null,
        sku,
        fulfillmentStatus: 'unfulfilled',
        fulfillableQuantity: item.quantity,
        fulfilledQuantity: 0,
        returnableQuantity: item.quantity,
        refundableQuantity: item.quantity,
        requiresShipping: product.requiresShipping,
        isTaxable: product.isTaxable,
        customAttributes: item.customAttributes || null,
        personalizedMessage: item.personalizedMessage || null,
        giftWrap: item.giftWrap || false,
        vendor: product.vendor,
        productType: product.productType
      })
    }

    // Calculate taxes and shipping (simplified)
    const totalTax = subtotal * 0.15 // 15% VAT for South Africa
    const totalShipping = body.shippingCost || 0
    const total = subtotal + totalTax + totalShipping

    // Create order
    const order = await prisma.order.create({
      data: {
        orderNumber: generateOrderNumber(),
        userId: body.userId || null,
        customerEmail: body.customerEmail,
        customerFirstName: body.customerFirstName || null,
        customerLastName: body.customerLastName || null,
        customerPhone: body.customerPhone || null,
        billingAddress: body.billingAddress,
        shippingAddress: body.shippingAddress,
        itemCount: processedItems.length,
        subtotal,
        totalDiscount: 0,
        totalTax,
        totalShipping,
        total,
        currency: 'ZAR',
        paymentStatus: 'pending',
        fulfillmentStatus: 'unfulfilled',
        status: 'pending',
        financialStatus: 'pending',
        customerNote: body.customerNote || null,
        source: body.source || 'web',
        sourceIdentifier: body.sourceIdentifier || null,
        attributes: body.attributes || null,
        tags: body.tags || [],
        items: {
          create: processedItems
        }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                title: true,
                slug: true
              }
            },
            variant: {
              select: {
                id: true,
                title: true,
                sku: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: order
    }, { status: 201 })

  } catch (error) {
    console.error('Create order API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    )
  }
}
