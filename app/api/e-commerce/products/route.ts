// Products API Routes
// GET /api/e-commerce/products - Search and list products
// POST /api/e-commerce/products - Create a new product

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'
import { ProductSearchParams, CreateProductInput } from '@/lib/ecommerce/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const params: ProductSearchParams = {
      query: searchParams.get('query') || undefined,
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      filters: {
        categoryIds: searchParams.get('categories') ? searchParams.get('categories')!.split(',') : undefined,
        tagIds: searchParams.get('tags') ? searchParams.get('tags')!.split(',') : undefined,
        vendor: searchParams.get('vendor') || undefined,
        productType: searchParams.get('productType') || undefined,
        status: searchParams.get('status') ? searchParams.get('status')!.split(',') as any : undefined,
        isVisible: searchParams.get('isVisible') ? searchParams.get('isVisible') === 'true' : undefined,
        inStock: searchParams.get('inStock') ? searchParams.get('inStock') === 'true' : undefined,
        onSale: searchParams.get('onSale') ? searchParams.get('onSale') === 'true' : undefined,
        priceRange: {
          min: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
          max: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
        }
      },
      sort: {
        field: (searchParams.get('sortBy') as any) || 'createdAt',
        direction: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
      }
    }

    // Remove empty price range if no values
    if (!params.filters?.priceRange?.min && !params.filters?.priceRange?.max) {
      delete params.filters?.priceRange
    }

    const result = await productService().searchProducts(params)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          data: result.data?.data || [],
          pagination: result.data?.pagination
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Products API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.title || !body.description || !body.price) {
      return NextResponse.json(
        { success: false, error: 'Title, description, and price are required' },
        { status: 400 }
      )
    }

    const input: CreateProductInput = {
      title: body.title,
      description: body.description,
      descriptionHtml: body.descriptionHtml,
      vendor: body.vendor,
      productType: body.productType,
      price: {
        amount: parseFloat(body.price),
        currency: body.currency || 'ZAR'
      },
      compareAtPrice: body.compareAtPrice ? {
        amount: parseFloat(body.compareAtPrice),
        currency: body.currency || 'ZAR'
      } : undefined,
      costPerItem: body.costPerItem ? {
        amount: parseFloat(body.costPerItem),
        currency: body.currency || 'ZAR'
      } : undefined,
      trackQuantity: body.trackQuantity ?? true,
      continueSellingWhenOutOfStock: body.continueSellingWhenOutOfStock ?? false,
      inventoryQuantity: parseInt(body.inventoryQuantity) || 0,
      weight: body.weight ? parseFloat(body.weight) : undefined,
      weightUnit: body.weightUnit,
      dimensions: body.dimensions ? {
        length: parseFloat(body.dimensions.length),
        width: parseFloat(body.dimensions.width),
        height: parseFloat(body.dimensions.height),
        unit: body.dimensions.unit
      } : undefined,
      images: body.images?.map((img: any, index: number) => ({
        url: img.url,
        altText: img.altText,
        position: index,
        width: img.width,
        height: img.height
      })),
      variants: body.variants?.map((variant: any) => ({
        sku: variant.sku,
        title: variant.title,
        price: {
          amount: parseFloat(variant.price),
          currency: body.currency || 'ZAR'
        },
        compareAtPrice: variant.compareAtPrice ? {
          amount: parseFloat(variant.compareAtPrice),
          currency: body.currency || 'ZAR'
        } : undefined,
        weight: variant.weight ? parseFloat(variant.weight) : undefined,
        weightUnit: variant.weightUnit,
        inventoryQuantity: parseInt(variant.inventoryQuantity) || 0,
        inventoryPolicy: variant.inventoryPolicy || 'deny',
        fulfillmentService: variant.fulfillmentService || 'manual',
        inventoryManagement: variant.inventoryManagement ?? true,
        available: variant.available ?? true,
        options: variant.options || []
      })),
      options: body.options?.map((option: any) => ({
        name: option.name,
        position: option.position || 0,
        values: option.values || []
      })),
      categoryIds: body.categoryIds,
      tagIds: body.tagIds,
      collectionIds: body.collectionIds,
      seo: body.seo ? {
        title: body.seo.title,
        description: body.seo.description,
        keywords: body.seo.keywords
      } : undefined,
      metafields: body.metafields,
      isGiftCard: body.isGiftCard ?? false,
      requiresShipping: body.requiresShipping ?? true,
      isTaxable: body.isTaxable ?? true,
      status: body.status || 'draft',
      isVisible: body.isVisible ?? true
    }

    const result = await productService().createProduct(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create product API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
