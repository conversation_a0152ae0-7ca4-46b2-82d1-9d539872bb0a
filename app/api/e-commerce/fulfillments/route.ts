// Fulfillment API Routes
// GET /api/e-commerce/fulfillments - List fulfillments
// POST /api/e-commerce/fulfillments - Create fulfillment

import { NextRequest, NextResponse } from 'next/server'
import { FulfillmentService } from '@/lib/ecommerce/services/fulfillment-service'
import { PrismaClient } from '@prisma/client'

const fulfillmentService = new FulfillmentService()
const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('orderId')
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    if (orderId) {
      // Get fulfillments for specific order
      const fulfillments = await fulfillmentService.getOrderFulfillments(orderId)
      return NextResponse.json({
        success: true,
        data: {
          data: fulfillments,
          pagination: {
            page: 1,
            limit: fulfillments.length,
            total: fulfillments.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      })
    }

    // Get all fulfillments with pagination and filtering
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    const where: any = {}
    if (status) {
      where.status = status
    }

    const [fulfillments, total] = await Promise.all([
      prisma.orderFulfillment.findMany({
        where,
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              customerEmail: true,
              customerFirstName: true,
              customerLastName: true,
              total: true,
              currency: true
            }
          },
          items: true
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.orderFulfillment.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        data: fulfillments,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Get fulfillments error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch fulfillments' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate required fields
    if (!body.orderId || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: orderId, items' },
        { status: 400 }
      )
    }

    if (!body.shippingMethod || !body.shippingMethod.carrier || !body.shippingMethod.service) {
      return NextResponse.json(
        { success: false, error: 'Missing required shipping method details' },
        { status: 400 }
      )
    }

    // Validate items structure
    for (const item of body.items) {
      if (!item.orderItemId || typeof item.quantity !== 'number' || item.quantity <= 0) {
        return NextResponse.json(
          { success: false, error: 'Invalid item structure. Each item must have orderItemId and positive quantity' },
          { status: 400 }
        )
      }
    }

    // Create fulfillment request
    const fulfillmentRequest = {
      orderId: body.orderId,
      items: body.items,
      shippingMethod: body.shippingMethod,
      fulfillmentCenter: body.fulfillmentCenter || 'main',
      priority: body.priority || 'normal',
      notes: body.notes,
      notifyCustomer: body.notifyCustomer !== false
    }

    // Process fulfillment
    const result = await fulfillmentService.processFulfillment(fulfillmentRequest)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          fulfillmentId: result.fulfillmentId,
          trackingNumber: result.trackingNumber,
          estimatedDelivery: result.estimatedDelivery
        },
        message: 'Fulfillment created successfully'
      })
    } else {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error,
          warnings: result.warnings
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Create fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create fulfillment' },
      { status: 500 }
    )
  }
}
