// Production-Ready Inventory API Routes
// GET /api/inventory - List inventory items
// POST /api/inventory - Update inventory

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const query = searchParams.get('query')
    const status = searchParams.get('status')
    const stockLevel = searchParams.get('stockLevel')

    if (productId) {
      // Get specific product inventory
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: {
          id: true,
          title: true,
          handle: true,
          inventoryQuantity: true,
          trackQuantity: true,
          costPerItem: true,
          vendor: true,
          updatedAt: true,
          isVisible: true,
          isAvailable: true,
          variants: {
            select: {
              id: true,
              title: true,
              sku: true,
              inventoryQuantity: true,
              costPerItem: true
            }
          }
        }
      })

      if (!product) {
        return NextResponse.json(
          { success: false, error: 'Product not found' },
          { status: 404 }
        )
      }

      // Transform to inventory format
      const inventoryItem = {
        id: product.id,
        productId: product.id,
        productTitle: product.title,
        productSku: product.handle,
        currentStock: product.inventoryQuantity,
        reservedStock: 0, // Would need separate tracking
        availableStock: product.inventoryQuantity,
        reorderPoint: 10, // Would be configurable
        maxStock: 100, // Would be configurable
        costPrice: Number(product.costPerItem || 0),
        lastUpdated: product.updatedAt.toISOString(),
        location: 'Main Warehouse',
        supplier: product.vendor,
        status: 'active'
      }

      return NextResponse.json({
        success: true,
        data: inventoryItem
      })
    } else {
      // Get all products as inventory items
      const where: any = {}

      if (query) {
        where.OR = [
          { title: { contains: query, mode: 'insensitive' } },
          { handle: { contains: query, mode: 'insensitive' } },
          { vendor: { contains: query, mode: 'insensitive' } }
        ]
      }

      if (status === 'active') {
        where.isVisible = true
        where.isAvailable = true
      } else if (status === 'inactive') {
        where.OR = [
          { isVisible: false },
          { isAvailable: false }
        ]
      }

      if (stockLevel === 'low-stock') {
        where.inventoryQuantity = { lte: 10 }
      } else if (stockLevel === 'out-of-stock') {
        where.inventoryQuantity = { lte: 0 }
      } else if (stockLevel === 'overstocked') {
        where.inventoryQuantity = { gte: 100 }
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          select: {
            id: true,
            title: true,
            handle: true,
            inventoryQuantity: true,
            trackQuantity: true,
            costPerItem: true,
            vendor: true,
            updatedAt: true,
            isVisible: true,
            isAvailable: true
          },
          orderBy: { updatedAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit
        }),
        prisma.product.count({ where })
      ])

      // Transform to inventory format
      const inventoryItems = products.map(product => ({
        id: product.id,
        productId: product.id,
        productTitle: product.title,
        productSku: product.handle,
        currentStock: product.inventoryQuantity,
        reservedStock: 0,
        availableStock: product.inventoryQuantity,
        reorderPoint: 10,
        maxStock: 100,
        costPrice: Number(product.costPerItem || 0),
        lastUpdated: product.updatedAt.toISOString(),
        location: 'Main Warehouse',
        supplier: product.vendor,
        status: (product.isVisible && product.isAvailable) ? 'active' : 'inactive'
      }))

      const totalPages = Math.ceil(total / limit)

      return NextResponse.json({
        success: true,
        data: {
          data: inventoryItems,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      })
    }

  } catch (error) {
    console.error('Inventory API GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.productId || typeof body.quantity !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Product ID and quantity are required' },
        { status: 400 }
      )
    }

    if (body.bulk && Array.isArray(body.updates)) {
      // Bulk update inventory
      const results = []

      for (const update of body.updates) {
        if (!update.productId || typeof update.quantity !== 'number') {
          continue
        }

        try {
          if (update.variantId) {
            // Update variant inventory
            await prisma.productVariant.update({
              where: { id: update.variantId },
              data: {
                inventoryQuantity: update.quantity,
                updatedAt: new Date()
              }
            })
          } else {
            // Update product inventory
            await prisma.product.update({
              where: { id: update.productId },
              data: {
                inventoryQuantity: update.quantity,
                updatedAt: new Date()
              }
            })
          }

          results.push({
            productId: update.productId,
            variantId: update.variantId,
            success: true
          })
        } catch (error) {
          results.push({
            productId: update.productId,
            variantId: update.variantId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Bulk inventory update completed',
        data: {
          results,
          updatedCount: results.filter(r => r.success).length,
          failedCount: results.filter(r => !r.success).length,
          timestamp: new Date().toISOString()
        }
      })
    } else {
      // Single update
      try {
        if (body.variantId) {
          // Update variant inventory
          const variant = await prisma.productVariant.update({
            where: { id: body.variantId },
            data: {
              inventoryQuantity: body.quantity,
              updatedAt: new Date()
            }
          })

          return NextResponse.json({
            success: true,
            message: 'Variant inventory updated successfully',
            data: {
              productId: body.productId,
              variantId: body.variantId,
              newQuantity: variant.inventoryQuantity,
              timestamp: new Date().toISOString()
            }
          })
        } else {
          // Update product inventory
          const product = await prisma.product.update({
            where: { id: body.productId },
            data: {
              inventoryQuantity: body.quantity,
              updatedAt: new Date()
            }
          })

          return NextResponse.json({
            success: true,
            message: 'Product inventory updated successfully',
            data: {
              productId: body.productId,
              newQuantity: product.inventoryQuantity,
              timestamp: new Date().toISOString()
            }
          })
        }
      } catch (error) {
        if (error instanceof Error && error.message.includes('Record to update not found')) {
          return NextResponse.json(
            { success: false, error: 'Product or variant not found' },
            { status: 404 }
          )
        }
        throw error
      }
    }

  } catch (error) {
    console.error('Inventory API POST error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}


