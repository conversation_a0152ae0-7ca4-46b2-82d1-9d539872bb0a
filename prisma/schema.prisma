generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                       String          @id @default(cuid())
  email                    String          @unique
  firstName                String?
  lastName                 String?
  displayName              String?
  phone                    String?
  dateOfBirth              DateTime?
  gender                   String?
  emailVerified            Boolean         @default(false)
  phoneVerified            Boolean         @default(false)
  lastLoginAt              DateTime?
  acceptsMarketing         Boolean         @default(false)
  preferredLanguage        String          @default("en")
  preferredCurrency        String          @default("ZAR")
  timezone                 String?
  avatar                   String?
  bio                      String?
  isActive                 Boolean         @default(true)
  isBlocked                Boolean         @default(false)
  defaultBillingAddressId  String?
  defaultShippingAddressId String?
  customerSince            DateTime?
  totalSpent               Decimal         @default(0) @db.Decimal(10, 2)
  orderCount               Int             @default(0)
  averageOrderValue        Decimal         @default(0) @db.Decimal(10, 2)
  lastOrderAt              DateTime?
  loyaltyPoints            Int?
  loyaltyTier              String?
  metafields               Json?
  tags                     String[]
  notes                    String?
  createdAt                DateTime        @default(now())
  updatedAt                DateTime        @updatedAt
  carts                    Cart[]
  orders                   Order[]
  paymentMethods           PaymentMethod[]
  reviews                  ProductReview[]
  activities               UserActivity[]
  addresses                UserAddress[]
  assignedRoles            UserRole[]      @relation("AssignedBy")
  userRoles                UserRole[]
  sessions                 UserSession[]
  wishlists                Wishlist[]

  @@map("users")
}

model UserAddress {
  id         String   @id @default(cuid())
  userId     String
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  province   String
  country    String
  postalCode String
  phone      String?
  isDefault  Boolean  @default(false)
  type       String
  label      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_addresses")
}

model UserSession {
  id             String   @id @default(cuid())
  userId         String
  token          String   @unique
  refreshToken   String?
  expiresAt      DateTime
  ipAddress      String?
  userAgent      String?
  isActive       Boolean  @default(true)
  lastActivityAt DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model UserActivity {
  id         String   @id @default(cuid())
  userId     String
  type       String
  entityType String?
  entityId   String?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  sessionId  String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_activities")
}

model Role {
  id                     String       @id @default(cuid())
  name                   String       @unique
  slug                   String       @unique
  description            String?
  color                  String?
  isSystem               Boolean      @default(false)
  isActive               Boolean      @default(true)
  level                  Int          @default(10)
  capabilities           Json         @default("{}")
  contentTypePermissions Json         @default("{}")
  restrictions           Json         @default("{}")
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  userRoles              UserRole[]
  permissions            Permission[] @relation("PermissionToRole")

  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  name        String
  resource    String
  action      String
  description String?
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  roles       Role[]   @relation("PermissionToRole")

  @@unique([resource, action])
  @@map("permissions")
}

model UserRole {
  id             String    @id @default(cuid())
  userId         String
  roleId         String
  assignedBy     String
  assignedAt     DateTime  @default(now())
  expiresAt      DateTime?
  isActive       Boolean   @default(true)
  metadata       Json?
  assignedByUser User      @relation("AssignedBy", fields: [assignedBy], references: [id])
  role           Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model DatabaseSchema {
  id          String   @id @default(cuid())
  name        String
  version     String
  description String?
  tables      Json     @default("[]")
  views       Json     @default("[]")
  functions   Json     @default("[]")
  triggers    Json     @default("[]")
  migrations  Json     @default("[]")
  metadata    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("database_schemas")
}

model Product {
  id                            String                      @id @default(cuid())
  title                         String
  slug                          String                      @unique
  description                   String
  descriptionHtml               String?
  vendor                        String?
  productType                   String?
  handle                        String                      @unique
  status                        String                      @default("draft")
  publishedAt                   DateTime?
  price                         Decimal                     @db.Decimal(10, 2)
  compareAtPrice                Decimal?                    @db.Decimal(10, 2)
  costPerItem                   Decimal?                    @db.Decimal(10, 2)
  currency                      String                      @default("ZAR")
  trackQuantity                 Boolean                     @default(true)
  continueSellingWhenOutOfStock Boolean                     @default(false)
  inventoryQuantity             Int                         @default(0)
  weight                        Decimal?                    @db.Decimal(8, 2)
  weightUnit                    String?
  dimensionLength               Decimal?                    @db.Decimal(8, 2)
  dimensionWidth                Decimal?                    @db.Decimal(8, 2)
  dimensionHeight               Decimal?                    @db.Decimal(8, 2)
  dimensionUnit                 String?
  hasVariants                   Boolean                     @default(false)
  seoTitle                      String?
  seoDescription                String?
  seoKeywords                   String[]
  metafields                    Json?
  isGiftCard                    Boolean                     @default(false)
  requiresShipping              Boolean                     @default(true)
  isTaxable                     Boolean                     @default(true)
  isVisible                     Boolean                     @default(true)
  isAvailable                   Boolean                     @default(true)
  availableForSale              Boolean                     @default(true)
  averageRating                 Decimal?                    @db.Decimal(3, 2)
  reviewCount                   Int                         @default(0)
  createdAt                     DateTime                    @default(now())
  updatedAt                     DateTime                    @updatedAt
  cartItems                     CartItem[]
  inventoryItems                InventoryItem[]
  orderItems                    OrderItem[]
  attributeValues               ProductAttributeValue[]
  bundleItems                   ProductBundleItem[]
  categories                    ProductCategoryRelation[]
  collections                   ProductCollectionRelation[]
  images                        ProductImage[]
  options                       ProductOption[]
  relations                     ProductRelation[]           @relation("ProductRelations")
  relatedTo                     ProductRelation[]           @relation("RelatedProducts")
  reviews                       ProductReview[]
  tags                          ProductTagRelation[]
  variants                      ProductVariant[]
  wishlistItems                 WishlistItem[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  position  Int
  width     Int?
  height    Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id                            String                 @id @default(cuid())
  productId                     String
  sku                           String                 @unique
  title                         String
  price                         Decimal                @db.Decimal(10, 2)
  compareAtPrice                Decimal?               @db.Decimal(10, 2)
  currency                      String                 @default("ZAR")
  weight                        Decimal?               @db.Decimal(8, 2)
  weightUnit                    String?
  inventoryQuantity             Int                    @default(0)
  inventoryPolicy               String                 @default("deny")
  fulfillmentService            String                 @default("manual")
  inventoryManagement           Boolean                @default(true)
  imageId                       String?
  available                     Boolean                @default(true)
  position                      Int                    @default(0)
  createdAt                     DateTime               @default(now())
  updatedAt                     DateTime               @updatedAt
  barcode                       String?
  continueSellingWhenOutOfStock Boolean                @default(false)
  costPerItem                   Decimal?               @db.Decimal(10, 2)
  metafields                    Json?
  requiresShipping              Boolean                @default(true)
  taxable                       Boolean                @default(true)
  trackQuantity                 Boolean                @default(true)
  cartItems                     CartItem[]
  inventoryItems                InventoryItem[]
  orderItems                    OrderItem[]
  bundleItems                   ProductBundleItem[]
  options                       ProductVariantOption[]
  product                       Product                @relation(fields: [productId], references: [id], onDelete: Cascade)
  wishlistItems                 WishlistItem[]

  @@map("product_variants")
}

model ProductVariantOption {
  id        String         @id @default(cuid())
  variantId String
  name      String
  value     String
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("product_variant_options")
}

model ProductOption {
  id        String   @id @default(cuid())
  productId String
  name      String
  position  Int
  values    String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_options")
}

model ProductCategory {
  id             String                    @id @default(cuid())
  name           String
  slug           String                    @unique
  description    String?
  image          String?
  parentId       String?
  position       Int                       @default(0)
  isVisible      Boolean                   @default(true)
  seoTitle       String?
  seoDescription String?
  createdAt      DateTime                  @default(now())
  updatedAt      DateTime                  @updatedAt
  parent         ProductCategory?          @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children       ProductCategory[]         @relation("CategoryHierarchy")
  products       ProductCategoryRelation[]

  @@map("product_categories")
}

model ProductCategoryRelation {
  id         String          @id @default(cuid())
  productId  String
  categoryId String
  createdAt  DateTime        @default(now())
  category   ProductCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  product    Product         @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, categoryId])
  @@map("product_category_relations")
}

model ProductTag {
  id          String               @id @default(cuid())
  name        String               @unique
  slug        String               @unique
  description String?
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  products    ProductTagRelation[]

  @@map("product_tags")
}

model ProductTagRelation {
  id        String     @id @default(cuid())
  productId String
  tagId     String
  createdAt DateTime   @default(now())
  product   Product    @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag       ProductTag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([productId, tagId])
  @@map("product_tag_relations")
}

model ProductCollection {
  id             String                      @id @default(cuid())
  title          String
  slug           String                      @unique
  description    String?
  image          String?
  sortOrder      String                      @default("manual")
  isVisible      Boolean                     @default(true)
  seoTitle       String?
  seoDescription String?
  createdAt      DateTime                    @default(now())
  updatedAt      DateTime                    @updatedAt
  products       ProductCollectionRelation[]

  @@map("product_collections")
}

model ProductCollectionRelation {
  id           String            @id @default(cuid())
  productId    String
  collectionId String
  position     Int               @default(0)
  createdAt    DateTime          @default(now())
  collection   ProductCollection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  product      Product           @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, collectionId])
  @@map("product_collection_relations")
}

model ProductReview {
  id           String   @id @default(cuid())
  productId    String
  userId       String
  userName     String
  userEmail    String
  rating       Int
  title        String?
  content      String
  isVerified   Boolean  @default(false)
  isApproved   Boolean  @default(false)
  helpfulCount Int      @default(0)
  images       String[]
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  product      Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("product_reviews")
}

model ProductAttribute {
  id          String                  @id @default(cuid())
  name        String                  @unique
  slug        String                  @unique
  type        String
  description String?
  isRequired  Boolean                 @default(false)
  isVariant   Boolean                 @default(false)
  isFilter    Boolean                 @default(false)
  position    Int                     @default(0)
  options     String[]
  validation  Json?
  createdAt   DateTime                @default(now())
  updatedAt   DateTime                @updatedAt
  values      ProductAttributeValue[]

  @@map("product_attributes")
}

model ProductAttributeValue {
  id          String           @id @default(cuid())
  productId   String
  attributeId String
  value       String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  attribute   ProductAttribute @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  product     Product          @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, attributeId])
  @@map("product_attribute_values")
}

model ProductBundle {
  id             String              @id @default(cuid())
  title          String
  slug           String              @unique
  description    String?
  price          Decimal             @db.Decimal(10, 2)
  compareAtPrice Decimal?            @db.Decimal(10, 2)
  currency       String              @default("ZAR")
  isActive       Boolean             @default(true)
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  items          ProductBundleItem[]

  @@map("product_bundles")
}

model ProductBundleItem {
  id        String          @id @default(cuid())
  bundleId  String
  productId String
  variantId String?
  quantity  Int             @default(1)
  discount  Decimal?        @db.Decimal(10, 2)
  position  Int             @default(0)
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  bundle    ProductBundle   @relation(fields: [bundleId], references: [id], onDelete: Cascade)
  product   Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant   ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("product_bundle_items")
}

model ProductRelation {
  id               String   @id @default(cuid())
  productId        String
  relatedProductId String
  type             String
  position         Int      @default(0)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  product          Product  @relation("ProductRelations", fields: [productId], references: [id], onDelete: Cascade)
  relatedProduct   Product  @relation("RelatedProducts", fields: [relatedProductId], references: [id], onDelete: Cascade)

  @@unique([productId, relatedProductId, type])
  @@map("product_relations")
}

model Cart {
  id               String            @id @default(cuid())
  sessionId        String?
  userId           String?
  itemCount        Int               @default(0)
  subtotal         Decimal           @default(0) @db.Decimal(10, 2)
  totalDiscount    Decimal           @default(0) @db.Decimal(10, 2)
  totalTax         Decimal           @default(0) @db.Decimal(10, 2)
  totalShipping    Decimal           @default(0) @db.Decimal(10, 2)
  total            Decimal           @default(0) @db.Decimal(10, 2)
  currency         String            @default("ZAR")
  status           String            @default("active")
  locale           String?
  customerNote     String?
  attributes       Json?
  lastActivityAt   DateTime          @default(now())
  expiresAt        DateTime?
  convertedAt      DateTime?
  orderId          String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  appliedDiscounts AppliedDiscount[]
  items            CartItem[]
  user             User?             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("carts")
}

model CartItem {
  id                  String          @id @default(cuid())
  cartId              String
  productId           String
  variantId           String?
  quantity            Int
  unitPrice           Decimal         @db.Decimal(10, 2)
  totalPrice          Decimal         @db.Decimal(10, 2)
  currency            String          @default("ZAR")
  productTitle        String
  productSlug         String
  productImage        String?
  variantTitle        String?
  isAvailable         Boolean         @default(true)
  maxQuantity         Int?
  customAttributes    Json?
  personalizedMessage String?
  giftWrap            Boolean         @default(false)
  compareAtPrice      Decimal?        @db.Decimal(10, 2)
  discountAmount      Decimal?        @db.Decimal(10, 2)
  discountReason      String?
  addedAt             DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  cart                Cart            @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product             Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant             ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("cart_items")
}

model AppliedDiscount {
  id              String   @id @default(cuid())
  cartId          String?
  orderId         String?
  code            String?
  title           String
  description     String?
  type            String
  value           Decimal  @db.Decimal(10, 2)
  amount          Decimal  @db.Decimal(10, 2)
  currency        String   @default("ZAR")
  applicableItems String[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  cart            Cart?    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  order           Order?   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("applied_discounts")
}

model Wishlist {
  id          String         @id @default(cuid())
  userId      String
  name        String
  description String?
  isPublic    Boolean        @default(false)
  itemCount   Int            @default(0)
  shareToken  String?        @unique
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  items       WishlistItem[]
  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("wishlists")
}

model WishlistItem {
  id            String          @id @default(cuid())
  wishlistId    String
  productId     String
  variantId     String?
  addedAt       DateTime        @default(now())
  priority      Int?
  notes         String?
  productTitle  String
  productSlug   String
  productImage  String?
  variantTitle  String?
  currentPrice  Decimal         @db.Decimal(10, 2)
  originalPrice Decimal?        @db.Decimal(10, 2)
  currency      String          @default("ZAR")
  isAvailable   Boolean         @default(true)
  isOnSale      Boolean         @default(false)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  product       Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant       ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)
  wishlist      Wishlist        @relation(fields: [wishlistId], references: [id], onDelete: Cascade)

  @@map("wishlist_items")
}

model Order {
  id                 String             @id @default(cuid())
  orderNumber        String             @unique
  cartId             String?
  userId             String?
  customerEmail      String
  customerFirstName  String?
  customerLastName   String?
  customerPhone      String?
  billingAddress     Json
  shippingAddress    Json
  itemCount          Int                @default(0)
  subtotal           Decimal            @db.Decimal(10, 2)
  totalDiscount      Decimal            @default(0) @db.Decimal(10, 2)
  totalTax           Decimal            @default(0) @db.Decimal(10, 2)
  totalShipping      Decimal            @default(0) @db.Decimal(10, 2)
  totalTip           Decimal            @default(0) @db.Decimal(10, 2)
  total              Decimal            @db.Decimal(10, 2)
  currency           String             @default("ZAR")
  paymentStatus      String             @default("pending")
  fulfillmentStatus  String             @default("unfulfilled")
  status             String             @default("pending")
  financialStatus    String             @default("pending")
  confirmedAt        DateTime?
  processedAt        DateTime?
  shippedAt          DateTime?
  deliveredAt        DateTime?
  cancelledAt        DateTime?
  customerNote       String?
  internalNotes      String[]
  locale             String?
  source             String             @default("web")
  sourceIdentifier   String?
  attributes         Json?
  tags               String[]
  riskLevel          String             @default("low")
  riskReasons        String[]
  acquisitionChannel String?
  referrer           String?
  utmSource          String?
  utmMedium          String?
  utmCampaign        String?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  appliedDiscounts   AppliedDiscount[]
  couponUsages       CouponUsage[]
  fulfillments       OrderFulfillment[]
  items              OrderItem[]
  refunds            OrderRefund[]
  returns            OrderReturn[]
  user               User?              @relation(fields: [userId], references: [id])
  payments           Payment[]

  @@map("orders")
}

model OrderItem {
  id                  String          @id @default(cuid())
  orderId             String
  productId           String
  variantId           String?
  quantity            Int
  unitPrice           Decimal         @db.Decimal(10, 2)
  totalPrice          Decimal         @db.Decimal(10, 2)
  currency            String          @default("ZAR")
  productTitle        String
  productSlug         String
  productImage        String?
  variantTitle        String?
  sku                 String?
  fulfillmentStatus   String          @default("unfulfilled")
  fulfillableQuantity Int
  fulfilledQuantity   Int             @default(0)
  compareAtPrice      Decimal?        @db.Decimal(10, 2)
  discountAmount      Decimal?        @db.Decimal(10, 2)
  discountReason      String?
  weight              Decimal?        @db.Decimal(8, 2)
  weightUnit          String?
  requiresShipping    Boolean         @default(true)
  isTaxable           Boolean         @default(true)
  customAttributes    Json?
  personalizedMessage String?
  giftWrap            Boolean         @default(false)
  returnableQuantity  Int
  refundableQuantity  Int
  vendor              String?
  productType         String?
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  order               Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product             Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant             ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("order_items")
}

model OrderFulfillment {
  id                  String                 @id @default(cuid())
  orderId             String
  status              String                 @default("pending")
  trackingNumber      String?
  trackingUrl         String?
  trackingCompany     String?
  locationId          String?
  locationName        String?
  shippedAt           DateTime?
  estimatedDeliveryAt DateTime?
  deliveredAt         DateTime?
  notifyCustomer      Boolean                @default(true)
  emailSent           Boolean                @default(false)
  smsSent             Boolean                @default(false)
  notes               String?
  receipt             Json?
  createdAt           DateTime               @default(now())
  updatedAt           DateTime               @updatedAt
  items               OrderFulfillmentItem[]
  order               Order                  @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_fulfillments")
}

model OrderFulfillmentItem {
  id            String           @id @default(cuid())
  fulfillmentId String
  orderItemId   String
  quantity      Int
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  fulfillment   OrderFulfillment @relation(fields: [fulfillmentId], references: [id], onDelete: Cascade)

  @@map("order_fulfillment_items")
}

model OrderReturn {
  id                   String            @id @default(cuid())
  orderId              String
  returnNumber         String            @unique
  status               String            @default("pending")
  reason               String
  reasonDetails        String?
  refundAmount         Decimal           @db.Decimal(10, 2)
  refundMethod         String
  returnShippingCost   Decimal?          @db.Decimal(10, 2)
  returnTrackingNumber String?
  requestedAt          DateTime          @default(now())
  approvedAt           DateTime?
  receivedAt           DateTime?
  processedAt          DateTime?
  refundedAt           DateTime?
  customerNote         String?
  internalNotes        String[]
  images               String[]
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  items                OrderReturnItem[]
  order                Order             @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_returns")
}

model OrderReturnItem {
  id          String      @id @default(cuid())
  returnId    String
  orderItemId String
  quantity    Int
  reason      String?
  condition   String
  restockable Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  return      OrderReturn @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("order_return_items")
}

model OrderRefund {
  id                     String            @id @default(cuid())
  orderId                String
  refundNumber           String            @unique
  amount                 Decimal           @db.Decimal(10, 2)
  currency               String            @default("ZAR")
  reason                 String
  shippingRefund         Decimal?          @db.Decimal(10, 2)
  paymentGatewayRefundId String?
  status                 String            @default("pending")
  processedAt            DateTime?
  notes                  String?
  notifyCustomer         Boolean           @default(true)
  createdAt              DateTime          @default(now())
  updatedAt              DateTime          @updatedAt
  items                  OrderRefundItem[]
  order                  Order             @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_refunds")
}

model OrderRefundItem {
  id          String      @id @default(cuid())
  refundId    String
  orderItemId String
  quantity    Int
  amount      Decimal     @db.Decimal(10, 2)
  currency    String      @default("ZAR")
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  refund      OrderRefund @relation(fields: [refundId], references: [id], onDelete: Cascade)

  @@map("order_refund_items")
}

model PaymentMethod {
  id                     String    @id @default(cuid())
  customerId             String?
  type                   String
  cardBrand              String?
  cardLast4              String?
  cardExpiryMonth        Int?
  cardExpiryYear         Int?
  cardFingerprint        String?
  cardFunding            String?
  cardCountry            String?
  cardIssuer             String?
  bankAccountType        String?
  bankRoutingNumber      String?
  bankLast4              String?
  bankName               String?
  bankCountry            String?
  digitalWalletProvider  String?
  digitalWalletEmail     String?
  digitalWalletPhone     String?
  billingAddress         Json?
  isDefault              Boolean   @default(false)
  isVerified             Boolean   @default(false)
  isActive               Boolean   @default(true)
  gatewayId              String
  gatewayCustomerId      String?
  gatewayPaymentMethodId String
  nickname               String?
  metadata               Json?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt
  user                   User?     @relation(fields: [customerId], references: [id], onDelete: Cascade)
  payments               Payment[]

  @@map("payment_methods")
}

model Payment {
  id                  String          @id @default(cuid())
  paymentNumber       String          @unique
  orderId             String?
  invoiceId           String?
  amount              Decimal         @db.Decimal(10, 2)
  amountCaptured      Decimal         @default(0) @db.Decimal(10, 2)
  amountRefunded      Decimal         @default(0) @db.Decimal(10, 2)
  currency            String          @default("ZAR")
  status              String          @default("pending")
  customerId          String?
  paymentMethodId     String?
  gatewayId           String
  gatewayPaymentId    String
  gatewayChargeId     String?
  processingFee       Decimal?        @db.Decimal(10, 2)
  netAmount           Decimal?        @db.Decimal(10, 2)
  authorizedAt        DateTime?
  capturedAt          DateTime?
  failedAt            DateTime?
  cancelledAt         DateTime?
  receiptEmail        String?
  receiptUrl          String?
  receiptNumber       String?
  disputed            Boolean         @default(false)
  disputeReason       String?
  disputeStatus       String?
  riskLevel           String          @default("low")
  riskScore           Int?
  riskReasons         String[]
  description         String?
  statementDescriptor String?
  metadata            Json?
  failureCode         String?
  failureMessage      String?
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  refunds             PaymentRefund[]
  order               Order?          @relation(fields: [orderId], references: [id])
  paymentMethod       PaymentMethod?  @relation(fields: [paymentMethodId], references: [id])

  @@map("payments")
}

model PaymentRefund {
  id              String    @id @default(cuid())
  refundNumber    String    @unique
  paymentId       String
  amount          Decimal   @db.Decimal(10, 2)
  currency        String    @default("ZAR")
  status          String    @default("pending")
  gatewayRefundId String
  reason          String
  reasonDetails   String?
  processedAt     DateTime?
  failedAt        DateTime?
  metadata        Json?
  failureCode     String?
  failureMessage  String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  payment         Payment   @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@map("payment_refunds")
}

model InventoryLocation {
  id                 String                 @id @default(cuid())
  name               String
  code               String                 @unique
  type               String
  address            Json?
  contactName        String?
  contactEmail       String?
  contactPhone       String?
  isActive           Boolean                @default(true)
  isPrimary          Boolean                @default(false)
  allowsInventory    Boolean                @default(true)
  allowsFulfillment  Boolean                @default(true)
  maxCapacity        Int?
  currentUtilization Int?
  operatingHours     Json?
  attributes         Json?
  notes              String?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  adjustments        InventoryAdjustment[]
  alerts             InventoryAlert[]
  counts             InventoryCount[]
  inventoryItems     InventoryItem[]
  movements          InventoryMovement[]
  reservations       InventoryReservation[]
  transfersFrom      InventoryTransfer[]    @relation("TransferFrom")
  transfersTo        InventoryTransfer[]    @relation("TransferTo")

  @@map("inventory_locations")
}

model InventoryItem {
  id                            String                    @id @default(cuid())
  productId                     String
  variantId                     String?
  sku                           String                    @unique
  name                          String
  description                   String?
  quantity                      Int                       @default(0)
  reservedQuantity              Int                       @default(0)
  availableQuantity             Int                       @default(0)
  committedQuantity             Int                       @default(0)
  lowStockThreshold             Int                       @default(5)
  outOfStockThreshold           Int                       @default(0)
  reorderPoint                  Int                       @default(10)
  reorderQuantity               Int                       @default(50)
  maxStockLevel                 Int?
  costPrice                     Decimal                   @db.Decimal(10, 2)
  averageCost                   Decimal                   @db.Decimal(10, 2)
  lastCostPrice                 Decimal?                  @db.Decimal(10, 2)
  currency                      String                    @default("ZAR")
  locationId                    String
  binLocation                   String?
  zone                          String?
  aisle                         String?
  shelf                         String?
  trackQuantity                 Boolean                   @default(true)
  allowBackorders               Boolean                   @default(false)
  continueSellingWhenOutOfStock Boolean                   @default(false)
  batchTracked                  Boolean                   @default(false)
  serialTracked                 Boolean                   @default(false)
  expiryTracked                 Boolean                   @default(false)
  status                        String                    @default("active")
  lastStockUpdate               DateTime                  @default(now())
  lastCountDate                 DateTime?
  nextCountDate                 DateTime?
  supplierId                    String?
  supplierSku                   String?
  leadTime                      Int?
  weight                        Decimal?                  @db.Decimal(8, 2)
  weightUnit                    String?
  dimensionLength               Decimal?                  @db.Decimal(8, 2)
  dimensionWidth                Decimal?                  @db.Decimal(8, 2)
  dimensionHeight               Decimal?                  @db.Decimal(8, 2)
  dimensionUnit                 String?
  attributes                    Json?
  tags                          String[]
  notes                         String?
  createdAt                     DateTime                  @default(now())
  updatedAt                     DateTime                  @updatedAt
  adjustmentItems               InventoryAdjustmentItem[]
  alerts                        InventoryAlert[]
  batches                       InventoryBatch[]
  countItems                    InventoryCountItem[]
  location                      InventoryLocation         @relation(fields: [locationId], references: [id], onDelete: Cascade)
  product                       Product                   @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant                       ProductVariant?           @relation(fields: [variantId], references: [id], onDelete: Cascade)
  movements                     InventoryMovement[]
  reservations                  InventoryReservation[]
  serials                       InventorySerial[]
  transferItems                 InventoryTransferItem[]

  @@map("inventory_items")
}

model InventoryMovement {
  id              String            @id @default(cuid())
  inventoryItemId String
  locationId      String
  type            String
  direction       String
  quantity        Int
  unitCost        Decimal?          @db.Decimal(10, 2)
  totalCost       Decimal?          @db.Decimal(10, 2)
  currency        String            @default("ZAR")
  referenceType   String?
  referenceId     String?
  referenceNumber String?
  batchNumber     String?
  serialNumbers   String[]
  expiryDate      DateTime?
  reason          String
  reasonCode      String?
  userId          String?
  userName        String?
  notes           String?
  attributes      Json?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  inventoryItem   InventoryItem     @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  location        InventoryLocation @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@map("inventory_movements")
}

model InventoryAdjustment {
  id               String                    @id @default(cuid())
  adjustmentNumber String                    @unique
  locationId       String
  reason           String
  reasonDetails    String?
  status           String                    @default("draft")
  requiresApproval Boolean                   @default(false)
  approvedBy       String?
  approvedAt       DateTime?
  rejectedBy       String?
  rejectedAt       DateTime?
  rejectionReason  String?
  createdBy        String
  notes            String?
  attachments      String[]
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt
  items            InventoryAdjustmentItem[]
  location         InventoryLocation         @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@map("inventory_adjustments")
}

model InventoryAdjustmentItem {
  id                 String              @id @default(cuid())
  adjustmentId       String
  inventoryItemId    String
  expectedQuantity   Int
  actualQuantity     Int
  adjustmentQuantity Int
  reason             String?
  unitCost           Decimal?            @db.Decimal(10, 2)
  totalCost          Decimal?            @db.Decimal(10, 2)
  currency           String              @default("ZAR")
  batchNumber        String?
  serialNumbers      String[]
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  adjustment         InventoryAdjustment @relation(fields: [adjustmentId], references: [id], onDelete: Cascade)
  inventoryItem      InventoryItem       @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)

  @@map("inventory_adjustment_items")
}

model InventoryCount {
  id                 String               @id @default(cuid())
  countNumber        String               @unique
  locationId         String
  type               String
  includeAllItems    Boolean              @default(true)
  categoryIds        String[]
  productIds         String[]
  tags               String[]
  status             String               @default("scheduled")
  scheduledDate      DateTime
  startedAt          DateTime?
  completedAt        DateTime?
  itemsExpected      Int                  @default(0)
  itemsCounted       Int                  @default(0)
  discrepanciesFound Int                  @default(0)
  totalVarianceValue Decimal?             @db.Decimal(10, 2)
  currency           String               @default("ZAR")
  assignedTo         String[]
  countedBy          String[]
  notes              String?
  instructions       String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  items              InventoryCountItem[]
  location           InventoryLocation    @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@map("inventory_counts")
}

model InventoryCountItem {
  id               String         @id @default(cuid())
  countId          String
  inventoryItemId  String
  expectedQuantity Int
  countedQuantity  Int?
  variance         Int?
  varianceValue    Decimal?       @db.Decimal(10, 2)
  currency         String         @default("ZAR")
  status           String         @default("pending")
  countedBy        String?
  countedAt        DateTime?
  notes            String?
  batchNumber      String?
  serialNumbers    String[]
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  count            InventoryCount @relation(fields: [countId], references: [id], onDelete: Cascade)
  inventoryItem    InventoryItem  @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)

  @@map("inventory_count_items")
}

model InventoryTransfer {
  id             String                  @id @default(cuid())
  transferNumber String                  @unique
  fromLocationId String
  toLocationId   String
  status         String                  @default("draft")
  requestedDate  DateTime                @default(now())
  shippedDate    DateTime?
  expectedDate   DateTime?
  receivedDate   DateTime?
  trackingNumber String?
  carrier        String?
  shippingCost   Decimal?                @db.Decimal(10, 2)
  currency       String                  @default("ZAR")
  requestedBy    String
  shippedBy      String?
  receivedBy     String?
  reason         String?
  notes          String?
  attachments    String[]
  createdAt      DateTime                @default(now())
  updatedAt      DateTime                @updatedAt
  items          InventoryTransferItem[]
  fromLocation   InventoryLocation       @relation("TransferFrom", fields: [fromLocationId], references: [id], onDelete: Cascade)
  toLocation     InventoryLocation       @relation("TransferTo", fields: [toLocationId], references: [id], onDelete: Cascade)

  @@map("inventory_transfers")
}

model InventoryTransferItem {
  id                String            @id @default(cuid())
  transferId        String
  inventoryItemId   String
  requestedQuantity Int
  shippedQuantity   Int?
  receivedQuantity  Int?
  damagedQuantity   Int?
  unitCost          Decimal?          @db.Decimal(10, 2)
  currency          String            @default("ZAR")
  batchNumber       String?
  serialNumbers     String[]
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  inventoryItem     InventoryItem     @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  transfer          InventoryTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)

  @@map("inventory_transfer_items")
}

model InventoryReservation {
  id              String            @id @default(cuid())
  inventoryItemId String
  locationId      String
  quantity        Int
  reservationType String
  referenceType   String
  referenceId     String
  referenceNumber String?
  status          String            @default("active")
  expiresAt       DateTime?
  fulfilledAt     DateTime?
  cancelledAt     DateTime?
  reservedBy      String
  reason          String?
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  inventoryItem   InventoryItem     @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  location        InventoryLocation @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@map("inventory_reservations")
}

model InventoryAlert {
  id               String            @id @default(cuid())
  type             String
  severity         String
  inventoryItemId  String
  locationId       String
  message          String
  currentValue     Int?
  thresholdValue   Int?
  status           String            @default("active")
  triggeredAt      DateTime          @default(now())
  acknowledgedAt   DateTime?
  resolvedAt       DateTime?
  dismissedAt      DateTime?
  acknowledgedBy   String?
  resolvedBy       String?
  dismissedBy      String?
  suggestedActions String[]
  actionsTaken     String[]
  metadata         Json?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  inventoryItem    InventoryItem     @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  location         InventoryLocation @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@map("inventory_alerts")
}

model InventoryBatch {
  id                  String            @id @default(cuid())
  batchNumber         String
  inventoryItemId     String
  locationId          String
  quantity            Int
  availableQuantity   Int
  costPrice           Decimal           @db.Decimal(10, 2)
  currency            String            @default("ZAR")
  expiryDate          DateTime?
  manufactureDate     DateTime?
  supplierId          String?
  supplierBatchNumber String?
  status              String            @default("active")
  attributes          Json?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  inventoryItem       InventoryItem     @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  serials             InventorySerial[]

  @@unique([batchNumber, inventoryItemId])
  @@map("inventory_batches")
}

model InventorySerial {
  id              String          @id @default(cuid())
  serialNumber    String
  inventoryItemId String
  locationId      String
  batchId         String?
  status          String          @default("available")
  costPrice       Decimal         @db.Decimal(10, 2)
  currency        String          @default("ZAR")
  manufactureDate DateTime?
  warrantyExpiry  DateTime?
  attributes      Json?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  batch           InventoryBatch? @relation(fields: [batchId], references: [id])
  inventoryItem   InventoryItem   @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)

  @@unique([serialNumber, inventoryItemId])
  @@map("inventory_serials")
}

model Page {
  id             String        @id @default(cuid())
  title          String
  slug           String        @unique
  description    String?
  status         String        @default("draft")
  type           String        @default("custom")
  template       String?
  seoTitle       String?
  seoDescription String?
  seoKeywords    String[]
  ogImage        String?
  publishedAt    DateTime?
  scheduledAt    DateTime?
  expiresAt      DateTime?
  isHomePage     Boolean       @default(false)
  isLandingPage  Boolean       @default(false)
  requiresAuth   Boolean       @default(false)
  allowComments  Boolean       @default(false)
  viewCount      Int           @default(0)
  shareCount     Int           @default(0)
  metadata       Json?
  customCss      String?
  customJs       String?
  createdBy      String?
  updatedBy      String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  blocks         PageBlock[]
  versions       PageVersion[]

  @@map("pages")
}

model PageBlock {
  id            String   @id @default(cuid())
  pageId        String
  blockType     String
  position      Int      @default(0)
  isVisible     Boolean  @default(true)
  configuration Json
  content       Json?
  styling       Json?
  responsive    Json?
  animation     Json?
  conditions    Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  page          Page     @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("page_blocks")
}

model PageVersion {
  id            String    @id @default(cuid())
  pageId        String
  versionNumber Int
  title         String
  description   String?
  configuration Json
  isPublished   Boolean   @default(false)
  publishedAt   DateTime?
  createdBy     String?
  createdAt     DateTime  @default(now())
  page          Page      @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, versionNumber])
  @@map("page_versions")
}

model PageTemplate {
  id            String   @id @default(cuid())
  name          String
  description   String?
  category      String
  thumbnail     String?
  configuration Json
  isPublic      Boolean  @default(true)
  isSystem      Boolean  @default(false)
  usageCount    Int      @default(0)
  tags          String[]
  createdBy     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("page_templates")
}

model BlockType {
  id            String   @id @default(cuid())
  name          String   @unique
  displayName   String
  description   String?
  category      String
  icon          String?
  thumbnail     String?
  defaultConfig Json
  configSchema  Json
  isActive      Boolean  @default(true)
  isSystem      Boolean  @default(false)
  version       String   @default("1.0.0")
  dependencies  String[]
  tags          String[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("block_types")
}

model PostType {
  id                  String   @id @default(cuid())
  name                String   @unique
  label               String
  labelPlural         String
  description         String?
  icon                String?
  isPublic            Boolean  @default(true)
  isHierarchical      Boolean  @default(false)
  hasArchive          Boolean  @default(true)
  supportsTitle       Boolean  @default(true)
  supportsContent     Boolean  @default(true)
  supportsExcerpt     Boolean  @default(false)
  supportsThumbnail   Boolean  @default(false)
  supportsComments    Boolean  @default(false)
  supportsRevisions   Boolean  @default(true)
  supportsPageBuilder Boolean  @default(false)
  menuPosition        Int?
  capabilities        Json?
  taxonomies          String[]
  customFields        Json     @default("[]")
  templates           String[]
  isSystem            Boolean  @default(false)
  isActive            Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  posts               Post[]

  @@map("post_types")
}

model Post {
  id               String             @id @default(cuid())
  title            String
  slug             String             @unique
  content          String?
  contentHtml      String?
  excerpt          String?
  status           String             @default("draft")
  postType         String
  parentId         String?
  menuOrder        Int                @default(0)
  featuredImage    String?
  featuredImageAlt String?
  template         String?
  password         String?
  publishedAt      DateTime?
  scheduledAt      DateTime?
  authorId         String?
  authorName       String?
  authorEmail      String?
  seoTitle         String?
  seoDescription   String?
  seoKeywords      String[]
  ogImage          String?
  ogTitle          String?
  ogDescription    String?
  twitterCard      String?
  canonicalUrl     String?
  metaRobots       String?
  viewCount        Int                @default(0)
  shareCount       Int                @default(0)
  likeCount        Int                @default(0)
  commentCount     Int                @default(0)
  usePageBuilder   Boolean            @default(false)
  pageBuilderData  Json?
  allowComments    Boolean            @default(true)
  allowPingbacks   Boolean            @default(true)
  isSticky         Boolean            @default(false)
  isFeatured       Boolean            @default(false)
  customFields     Json?
  metadata         Json?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  blocks           PostBlock[]
  comments         PostComment[]
  meta             PostMeta[]
  revisions        PostRevision[]
  taxonomyTerms    PostTaxonomyTerm[]
  parent           Post?              @relation("PostHierarchy", fields: [parentId], references: [id])
  children         Post[]             @relation("PostHierarchy")
  postTypeRef      PostType           @relation(fields: [postType], references: [name])

  @@index([postType])
  @@index([status])
  @@index([publishedAt])
  @@index([authorId])
  @@index([parentId])
  @@map("posts")
}

model PostMeta {
  id        String   @id @default(cuid())
  postId    String
  metaKey   String
  metaValue String?
  metaType  String   @default("string")
  isPrivate Boolean  @default(false)
  autoload  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([postId, metaKey])
  @@index([metaKey])
  @@map("post_meta")
}

model Taxonomy {
  id              String         @id @default(cuid())
  name            String         @unique
  label           String
  labelPlural     String
  description     String?
  isHierarchical  Boolean        @default(false)
  isPublic        Boolean        @default(true)
  showInMenu      Boolean        @default(true)
  showInRest      Boolean        @default(true)
  postTypes       String[]
  capabilities    Json?
  metaBoxCallback String?
  isSystem        Boolean        @default(false)
  isActive        Boolean        @default(true)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  terms           TaxonomyTerm[]

  @@map("taxonomies")
}

model TaxonomyTerm {
  id          String             @id @default(cuid())
  name        String
  slug        String
  description String?
  taxonomyId  String
  parentId    String?
  count       Int                @default(0)
  color       String?
  image       String?
  metadata    Json?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  posts       PostTaxonomyTerm[]
  parent      TaxonomyTerm?      @relation("TermHierarchy", fields: [parentId], references: [id])
  children    TaxonomyTerm[]     @relation("TermHierarchy")
  taxonomy    Taxonomy           @relation(fields: [taxonomyId], references: [id], onDelete: Cascade)

  @@unique([taxonomyId, slug])
  @@index([taxonomyId])
  @@index([parentId])
  @@map("taxonomy_terms")
}

model PostTaxonomyTerm {
  id        String       @id @default(cuid())
  postId    String
  termId    String
  createdAt DateTime     @default(now())
  post      Post         @relation(fields: [postId], references: [id], onDelete: Cascade)
  term      TaxonomyTerm @relation(fields: [termId], references: [id], onDelete: Cascade)

  @@unique([postId, termId])
  @@map("post_taxonomy_terms")
}

model PostComment {
  id          String        @id @default(cuid())
  postId      String
  parentId    String?
  authorName  String
  authorEmail String
  authorUrl   String?
  authorIp    String?
  content     String
  status      String        @default("pending")
  type        String        @default("comment")
  userAgent   String?
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  parent      PostComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies     PostComment[] @relation("CommentReplies")
  post        Post          @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([status])
  @@index([parentId])
  @@map("post_comments")
}

model PostRevision {
  id             String   @id @default(cuid())
  postId         String
  title          String
  content        String?
  contentHtml    String?
  excerpt        String?
  revisionNumber Int
  changeType     String   @default("revision")
  changeSummary  String?
  authorId       String?
  authorName     String?
  metadata       Json?
  createdAt      DateTime @default(now())
  post           Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([postId, revisionNumber])
  @@index([postId])
  @@map("post_revisions")
}

model PostBlock {
  id            String   @id @default(cuid())
  postId        String
  blockType     String
  position      Int      @default(0)
  isVisible     Boolean  @default(true)
  configuration Json
  content       Json?
  styling       Json?
  responsive    Json?
  animation     Json?
  conditions    Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  post          Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([position])
  @@map("post_blocks")
}

model BlogPost {
  id               String             @id @default(cuid())
  title            String
  slug             String             @unique
  excerpt          String?
  content          String
  featuredImage    String?
  featuredImageAlt String?
  status           String             @default("draft")
  publishedAt      DateTime?
  scheduledAt      DateTime?
  authorId         String?
  authorName       String?
  authorEmail      String?
  seoTitle         String?
  seoDescription   String?
  seoKeywords      String[]
  ogImage          String?
  allowComments    Boolean            @default(true)
  isFeatured       Boolean            @default(false)
  viewCount        Int                @default(0)
  shareCount       Int                @default(0)
  readingTime      Int?
  metadata         Json?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  comments         BlogComment[]
  categories       BlogPostCategory[]
  tags             BlogPostTag[]

  @@map("blog_posts")
}

model BlogCategory {
  id             String             @id @default(cuid())
  name           String
  slug           String             @unique
  description    String?
  color          String?
  image          String?
  parentId       String?
  isVisible      Boolean            @default(true)
  sortOrder      Int                @default(0)
  postCount      Int                @default(0)
  seoTitle       String?
  seoDescription String?
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  parent         BlogCategory?      @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children       BlogCategory[]     @relation("CategoryHierarchy")
  posts          BlogPostCategory[]

  @@map("blog_categories")
}

model BlogTag {
  id          String        @id @default(cuid())
  name        String
  slug        String        @unique
  description String?
  color       String?
  isVisible   Boolean       @default(true)
  postCount   Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  posts       BlogPostTag[]

  @@map("blog_tags")
}

model BlogPostCategory {
  id         String       @id @default(cuid())
  postId     String
  categoryId String
  createdAt  DateTime     @default(now())
  category   BlogCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  post       BlogPost     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([postId, categoryId])
  @@map("blog_post_categories")
}

model BlogPostTag {
  id        String   @id @default(cuid())
  postId    String
  tagId     String
  createdAt DateTime @default(now())
  post      BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag       BlogTag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([postId, tagId])
  @@map("blog_post_tags")
}

model BlogComment {
  id            String        @id @default(cuid())
  postId        String
  parentId      String?
  authorName    String
  authorEmail   String
  authorWebsite String?
  content       String
  isApproved    Boolean       @default(false)
  isSpam        Boolean       @default(false)
  ipAddress     String?
  userAgent     String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  parent        BlogComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies       BlogComment[] @relation("CommentReplies")
  post          BlogPost      @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@map("blog_comments")
}

model AdminUser {
  id               String          @id @default(cuid())
  email            String          @unique
  firstName        String
  lastName         String
  displayName      String
  passwordHash     String
  lastLoginAt      DateTime?
  loginAttempts    Int             @default(0)
  lockedUntil      DateTime?
  avatar           String?
  phone            String?
  timezone         String          @default("UTC")
  locale           String          @default("en")
  isActive         Boolean         @default(true)
  isEmailVerified  Boolean         @default(false)
  twoFactorEnabled Boolean         @default(false)
  twoFactorSecret  String?
  backupCodes      String[]
  metadata         Json?
  notes            String?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  activities       AdminActivity[]
  sessions         AdminSession[]
  roles            AdminUserRole[]

  @@map("admin_users")
}

model AdminRole {
  id           String          @id @default(cuid())
  name         String          @unique
  description  String?
  permissions  String[]
  isSystemRole Boolean         @default(false)
  userCount    Int             @default(0)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  users        AdminUserRole[]

  @@map("admin_roles")
}

model AdminUserRole {
  id        String    @id @default(cuid())
  userId    String
  roleId    String
  createdAt DateTime  @default(now())
  role      AdminRole @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user      AdminUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("admin_user_roles")
}

model AdminSession {
  id             String    @id @default(cuid())
  userId         String
  token          String    @unique
  ipAddress      String
  userAgent      String
  isActive       Boolean   @default(true)
  lastActivityAt DateTime  @default(now())
  expiresAt      DateTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  user           AdminUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_sessions")
}

model AdminActivity {
  id          String    @id @default(cuid())
  userId      String
  userName    String
  action      String
  resource    String
  resourceId  String?
  description String
  changes     Json?
  ipAddress   String
  userAgent   String
  sessionId   String
  metadata    Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        AdminUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_activities")
}

model Layout {
  id          String             @id @default(cuid())
  name        String
  description String?
  type        String             @default("page")
  category    String             @default("custom")
  structure   Json               @default("{}")
  styling     Json               @default("{}")
  responsive  Json               @default("{}")
  conditions  Json               @default("{}")
  isTemplate  Boolean            @default(false)
  isSystem    Boolean            @default(false)
  isActive    Boolean            @default(true)
  usageCount  Int                @default(0)
  thumbnail   String?
  tags        String[]
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  assignments LayoutAssignment[]
  sections    LayoutSection[]
  versions    LayoutVersion[]

  @@index([type])
  @@index([category])
  @@index([isActive])
  @@index([isTemplate])
  @@map("layouts")
}

model LayoutAssignment {
  id         String   @id @default(cuid())
  layoutId   String
  targetType String
  targetId   String?
  targetSlug String?
  priority   Int      @default(0)
  conditions Json     @default("{}")
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  layout     Layout   @relation(fields: [layoutId], references: [id], onDelete: Cascade)

  @@index([targetType, targetId])
  @@index([priority])
  @@map("layout_assignments")
}

model LayoutSection {
  id            String        @id @default(cuid())
  layoutId      String
  name          String
  type          String
  position      Int           @default(0)
  configuration Json          @default("{}")
  styling       Json          @default("{}")
  responsive    Json          @default("{}")
  blocks        Json          @default("[]")
  isVisible     Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  layoutBlocks  LayoutBlock[]
  layout        Layout        @relation(fields: [layoutId], references: [id], onDelete: Cascade)

  @@index([layoutId])
  @@index([type])
  @@map("layout_sections")
}

model LayoutBlock {
  id            String        @id @default(cuid())
  sectionId     String
  name          String
  type          String
  position      Int           @default(0)
  configuration Json          @default("{}")
  content       Json          @default("{}")
  styling       Json          @default("{}")
  responsive    Json          @default("{}")
  conditions    Json          @default("{}")
  isVisible     Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  section       LayoutSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)

  @@index([sectionId])
  @@index([type])
  @@map("layout_blocks")
}

model LayoutVersion {
  id            String   @id @default(cuid())
  layoutId      String
  versionNumber Int
  name          String?
  description   String?
  structure     Json
  styling       Json     @default("{}")
  responsive    Json     @default("{}")
  isPublished   Boolean  @default(false)
  isCurrent     Boolean  @default(false)
  createdBy     String?
  createdAt     DateTime @default(now())
  layout        Layout   @relation(fields: [layoutId], references: [id], onDelete: Cascade)

  @@unique([layoutId, versionNumber])
  @@index([layoutId])
  @@map("layout_versions")
}

model NavigationMenu {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  items       Json     @default("[]")
  settings    Json     @default("{}")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([slug])
  @@map("navigation_menus")
}

model WidgetArea {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  widgets     Json     @default("[]")
  settings    Json     @default("{}")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([slug])
  @@map("widget_areas")
}

model Theme {
  id           String             @id @default(cuid())
  name         String
  description  String?
  version      String             @default("1.0.0")
  author       String?
  category     String
  tags         String[]
  preview      String?
  config       Json
  isDefault    Boolean            @default(false)
  isActive     Boolean            @default(false)
  createdBy    String             @default("system")
  updatedBy    String             @default("system")
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  applications ThemeApplication[]

  @@map("themes")
}

model ThemeApplication {
  id             String   @id @default(cuid())
  scope          String
  targetId       String?
  themeId        String
  customizations Json?
  appliedAt      DateTime @default(now())
  appliedBy      String   @default("system")
  isActive       Boolean  @default(true)
  theme          Theme    @relation(fields: [themeId], references: [id], onDelete: Cascade)

  @@unique([scope, targetId, themeId])
  @@map("theme_applications")
}

model Workflow {
  id          String              @id @default(cuid())
  name        String
  description String?
  version     String              @default("1.0.0")
  category    String
  trigger     Json
  steps       Json
  isActive    Boolean             @default(true)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  createdBy   String              @default("system")
  tags        String[]
  metadata    Json?
  executions  WorkflowExecution[]

  @@map("workflows")
}

model WorkflowExecution {
  id              String    @id @default(cuid())
  workflowId      String
  workflowVersion String
  status          String
  triggeredBy     String
  triggerData     Json
  context         Json
  steps           Json
  startedAt       DateTime  @default(now())
  completedAt     DateTime?
  duration        Int?
  error           String?
  result          Json?
  workflow        Workflow  @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@index([workflowId])
  @@index([status])
  @@index([startedAt])
  @@map("workflow_executions")
}

model NewsletterSubscriber {
  id             String    @id @default(cuid())
  email          String    @unique
  firstName      String?
  lastName       String?
  isActive       Boolean   @default(true)
  preferences    Json      @default("{}")
  subscribedAt   DateTime  @default(now())
  unsubscribedAt DateTime?

  @@map("newsletter_subscribers")
}

model Customer {
  id           String            @id @default(cuid())
  email        String            @unique
  firstName    String
  lastName     String
  phone        String?
  dateOfBirth  String?
  preferences  Json              @default("{}")
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  couponUsages CouponUsage[]
  addresses    CustomerAddress[]

  @@map("customers")
}

model CustomerAddress {
  id         String   @id @default(cuid())
  customerId String
  type       String
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  province   String
  postalCode String
  country    String
  phone      String?
  isDefault  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_addresses")
}

model StockMovement {
  id        String   @id @default(cuid())
  productId String
  variantId String?
  type      String
  quantity  Int
  reason    String
  reference String?
  createdAt DateTime @default(now())

  @@map("stock_movements")
}

model Coupon {
  id                    String        @id @default(cuid())
  code                  String        @unique
  name                  String
  description           String?
  type                  String
  value                 Float
  minimumOrderAmount    Float?
  maximumDiscountAmount Float?
  usageLimit            Int?
  usageCount            Int           @default(0)
  customerUsageLimit    Int?
  isActive              Boolean       @default(true)
  startsAt              DateTime?
  expiresAt             DateTime?
  applicableProducts    String[]      @default([])
  applicableCategories  String[]      @default([])
  excludedProducts      String[]      @default([])
  excludedCategories    String[]      @default([])
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  usages                CouponUsage[]

  @@map("coupons")
}

model CouponUsage {
  id             String    @id @default(cuid())
  couponId       String
  orderId        String
  customerId     String?
  discountAmount Float
  createdAt      DateTime  @default(now())
  coupon         Coupon    @relation(fields: [couponId], references: [id], onDelete: Cascade)
  customer       Customer? @relation(fields: [customerId], references: [id])
  order          Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("coupon_usages")
}

model SiteSettings {
  id              String   @id @default(cuid())
  homepageId      String?
  siteName        String
  siteDescription String
  siteUrl         String
  logoUrl         String?
  faviconUrl      String?
  socialMedia     Json     @default("{}")
  seo             Json     @default("{}")
  ecommerce       Json     @default("{}")
  maintenance     Json     @default("{}")
  analytics       Json     @default("{}")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("site_settings")
}
