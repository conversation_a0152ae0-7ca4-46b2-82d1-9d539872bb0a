'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Copy, 
  Trash2, 
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useProducts, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { useCategories } from '@/lib/ecommerce/hooks/use-categories'
import { BulkProductOperations } from './bulk-product-operations'
import { formatCurrency } from '@/lib/utils'
import type { Product } from '@/lib/ecommerce/types'

interface EnhancedProductListProps {
  onCreateProduct: () => void
  onEditProduct: (product: Product) => void
  onViewProduct: (product: Product) => void
}

export function EnhancedProductList({ 
  onCreateProduct, 
  onEditProduct, 
  onViewProduct 
}: EnhancedProductListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'draft' | 'archived'>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [sortField, setSortField] = useState<'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating'>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const { categories } = useCategories()
  const { 
    duplicateProduct, 
    deleteProduct, 
    loading: mutationLoading, 
    error: mutationError,
    clearError 
  } = useProductMutations()

  // Build search parameters using useMemo to prevent unnecessary re-renders
  const searchParams = useMemo(() => ({
    query: searchQuery || undefined,
    filters: {
      status: selectedStatus !== 'all' ? [selectedStatus] : undefined,
      categoryIds: selectedCategory !== 'all' ? [selectedCategory] : undefined
    },
    sort: {
      field: sortField,
      direction: sortDirection
    },
    page: currentPage,
    limit: 20
  }), [searchQuery, selectedStatus, selectedCategory, sortField, sortDirection, currentPage])

  const { products, loading, error, pagination, refetch, searchProducts } = useProducts({
    initialParams: searchParams
  })

  // Update search when parameters change (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchProducts(searchParams)
    }, 300) // 300ms debounce

    return () => clearTimeout(timeoutId)
  }, [searchParams, searchProducts])

  // Handle product selection
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  // Handle product actions
  const handleDuplicateProduct = async (product: Product) => {
    const duplicated = await duplicateProduct(product.id)
    if (duplicated) {
      refetch()
    }
  }

  const handleDeleteProduct = async (product: Product) => {
    if (confirm(`Are you sure you want to delete "${product.title}"?`)) {
      const deleted = await deleteProduct(product.id)
      if (deleted) {
        refetch()
      }
    }
  }

  // Status badge styling
  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      draft: 'secondary',
      archived: 'outline'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Stock status indicator
  const getStockIndicator = (product: Product) => {
    if (!product.trackQuantity) {
      return <Badge variant="outline">Not tracked</Badge>
    }

    if (product.inventoryQuantity <= 0) {
      return <Badge variant="destructive">Out of stock</Badge>
    }

    if (product.inventoryQuantity <= 5) {
      return <Badge variant="secondary">Low stock</Badge>
    }

    return <Badge variant="default">In stock</Badge>
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as 'all' | 'active' | 'draft' | 'archived')}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">Newest first</SelectItem>
                <SelectItem value="createdAt-asc">Oldest first</SelectItem>
                <SelectItem value="title-asc">Name A-Z</SelectItem>
                <SelectItem value="title-desc">Name Z-A</SelectItem>
                <SelectItem value="price-asc">Price low to high</SelectItem>
                <SelectItem value="price-desc">Price high to low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedProducts.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4" />
                <span className="font-medium">
                  {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4">
                <BulkProductOperations
                  selectedProducts={selectedProducts}
                  products={products.map(p => ({
                    id: p.id,
                    title: p.title,
                    status: p.status,
                    price: p.price.amount
                  }))}
                  onOperationComplete={() => {
                    setSelectedProducts([])
                    setShowBulkOperations(false)
                    refetch()
                  }}
                  onClearSelection={() => {
                    setSelectedProducts([])
                    setShowBulkOperations(false)
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {(error || mutationError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error?.message || mutationError?.message || 'An error occurred'}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => {
                clearError()
                refetch()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>Products</CardTitle>
          <CardDescription>
            {pagination ? `${pagination.total} total products` : 'Loading products...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading products...</span>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No products found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                Get started by creating your first product.
              </p>
              <Button className="mt-4" onClick={onCreateProduct}>
                <Plus className="mr-2 h-4 w-4" />
                Add Product
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedProducts.length === products.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={(checked) => 
                            handleSelectProduct(product.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          {product.images?.[0] ? (
                            <img
                              src={product.images[0].url}
                              alt={product.images[0].altText}
                              className="h-10 w-10 rounded object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <div className={`h-10 w-10 rounded bg-muted flex items-center justify-center ${product.images?.[0] ? 'hidden' : ''}`}>
                            <Package className="h-5 w-5 text-muted-foreground" />
                          </div>
                          <div>
                            <div className="font-medium">{product.title}</div>
                            <div className="text-sm text-muted-foreground">
                              {product.vendor}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(product.status)}</TableCell>
                      <TableCell>{getStockIndicator(product)}</TableCell>
                      <TableCell>
                        <div>
                          {formatCurrency(product.price.amount, product.price.currency)}
                          {product.compareAtPrice && (
                            <div className="text-sm text-muted-foreground line-through">
                              {formatCurrency(product.compareAtPrice.amount, product.compareAtPrice.currency)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {product.categories?.[0]?.name || '-'}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onViewProduct(product)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEditProduct(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteProduct(product)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} products
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
