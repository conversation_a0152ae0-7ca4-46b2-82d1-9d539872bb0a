'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Package, AlertTriangle, RefreshCw } from 'lucide-react'
import { useProducts } from '@/lib/ecommerce/hooks/use-products'
import { formatCurrency } from '@/lib/utils'

export function DebugProductList() {
  const [debugInfo, setDebugInfo] = useState<any>({})
  
  const { products, loading, error, pagination, refetch, searchProducts } = useProducts({
    initialParams: {
      page: 1,
      limit: 10
    }
  })

  // Debug effect to track state changes
  useEffect(() => {
    setDebugInfo({
      productsCount: products.length,
      loading,
      error,
      pagination,
      timestamp: new Date().toISOString()
    })
  }, [products, loading, error, pagination])

  const handleTestFetch = async () => {
    console.log('🔍 Testing direct API fetch...')
    try {
      const response = await fetch('/api/e-commerce/products?page=1&limit=5')
      const result = await response.json()
      console.log('📦 API Response:', result)
      
      if (result.success) {
        console.log('✅ API working, products:', result.data?.data?.length || 0)
      } else {
        console.error('❌ API error:', result.error)
      }
    } catch (err) {
      console.error('❌ Network error:', err)
    }
  }

  const handleRefresh = () => {
    console.log('🔄 Refreshing products...')
    refetch()
  }

  return (
    <div className="space-y-6">
      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="mr-2 h-5 w-5" />
            Debug Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Products Count:</strong> {debugInfo.productsCount || 0}
            </div>
            <div>
              <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Error:</strong> {error ? error.message : 'None'}
            </div>
            <div>
              <strong>Pagination:</strong> {pagination ? `Page ${pagination.page} of ${pagination.totalPages}` : 'None'}
            </div>
            <div>
              <strong>Total Products:</strong> {pagination?.total || 'Unknown'}
            </div>
            <div>
              <strong>Last Update:</strong> {debugInfo.timestamp ? new Date(debugInfo.timestamp).toLocaleTimeString() : 'Never'}
            </div>
          </div>
          
          <div className="flex space-x-2 mt-4">
            <Button onClick={handleTestFetch} variant="outline" size="sm">
              Test API Direct
            </Button>
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Error:</strong> {error.message}
            <br />
            <strong>Code:</strong> {error.code}
          </AlertDescription>
        </Alert>
      )}

      {/* Products Display */}
      <Card>
        <CardHeader>
          <CardTitle>Products ({products.length})</CardTitle>
          <CardDescription>
            {pagination ? `Showing ${products.length} of ${pagination.total} products` : 'Loading...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading products...</span>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No products found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {error ? 'There was an error loading products.' : 'No products available.'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    {product.images?.[0] ? (
                      <img
                        src={product.images[0].url}
                        alt={product.images[0].altText}
                        className="h-12 w-12 rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="h-12 w-12 rounded bg-muted flex items-center justify-center">
                        <Package className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                    <div>
                      <div className="font-medium">{product.title}</div>
                      <div className="text-sm text-muted-foreground">
                        ID: {product.id} | Status: {product.status}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {formatCurrency(product.price.amount, product.price.currency)}
                    </div>
                    <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                      {product.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Raw Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Debug Data</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-muted p-4 rounded overflow-auto max-h-64">
            {JSON.stringify({ products: products.slice(0, 2), pagination, error }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
