#!/usr/bin/env tsx

/**
 * Test script to verify e-commerce API endpoints
 * Run this to check if the product loading system is working
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  
  try {
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Test product count
    const productCount = await prisma.product.count()
    console.log(`📦 Found ${productCount} products in database`)
    
    if (productCount > 0) {
      // Get sample products
      const sampleProducts = await prisma.product.findMany({
        take: 3,
        include: {
          images: true,
          categories: {
            include: {
              category: true
            }
          }
        }
      })
      
      console.log('📋 Sample products:')
      sampleProducts.forEach((product, index) => {
        console.log(`  ${index + 1}. ${product.title} - R${product.price} (${product.status})`)
        console.log(`     Images: ${product.images.length}`)
        console.log(`     Categories: ${product.categories.length}`)
      })
    }
    
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    return false
  }
}

async function testProductService() {
  console.log('\n🔍 Testing ProductService...')
  
  try {
    const { productService } = await import('../lib/ecommerce')
    
    // Test search products
    const searchResult = await productService().searchProducts({
      page: 1,
      limit: 5
    })
    
    if (searchResult.success) {
      console.log('✅ ProductService.searchProducts() working')
      console.log(`📦 Retrieved ${searchResult.data?.data?.length || 0} products`)
      console.log(`📄 Pagination: Page ${searchResult.data?.pagination?.page} of ${searchResult.data?.pagination?.totalPages}`)
    } else {
      console.error('❌ ProductService.searchProducts() failed:', searchResult.error)
      return false
    }
    
    // Test get single product if products exist
    if (searchResult.data?.data?.length > 0) {
      const firstProduct = searchResult.data.data[0]
      const productResult = await productService().getProductById(firstProduct.id)
      
      if (productResult.success) {
        console.log('✅ ProductService.getProductById() working')
        console.log(`📦 Retrieved product: ${productResult.data?.title}`)
      } else {
        console.error('❌ ProductService.getProductById() failed:', productResult.error)
        return false
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ ProductService test failed:', error.message)
    return false
  }
}

async function testApiEndpoints() {
  console.log('\n🔍 Testing API endpoints...')
  
  try {
    // We can't test HTTP endpoints directly without a server running
    // But we can check if the route files exist
    const fs = await import('fs')
    const path = await import('path')
    
    const apiRoutes = [
      'app/api/e-commerce/products/route.ts',
      'app/api/e-commerce/products/[id]/route.ts',
      'app/api/e-commerce/products/slug/[slug]/route.ts'
    ]
    
    let allRoutesExist = true
    
    for (const route of apiRoutes) {
      const routePath = path.join(process.cwd(), route)
      if (fs.existsSync(routePath)) {
        console.log(`✅ Route exists: ${route}`)
      } else {
        console.log(`❌ Route missing: ${route}`)
        allRoutesExist = false
      }
    }
    
    return allRoutesExist
  } catch (error) {
    console.error('❌ API endpoint test failed:', error.message)
    return false
  }
}

async function testDataTransformation() {
  console.log('\n🔍 Testing data transformation...')
  
  try {
    // Get a raw product from database
    const rawProduct = await prisma.product.findFirst({
      include: {
        images: true,
        categories: {
          include: {
            category: true
          }
        },
        tags: {
          include: {
            tag: true
          }
        }
      }
    })
    
    if (!rawProduct) {
      console.log('⚠️  No products found for transformation test')
      return true
    }
    
    // Test transformation
    const { ProductService } = await import('../lib/ecommerce/services/product-service')
    const productService = new ProductService()
    
    // Access the private method through a workaround
    const transformedProduct = productService.transformProductData(rawProduct)
    
    console.log('✅ Data transformation working')
    console.log(`📦 Transformed product: ${transformedProduct.title}`)
    console.log(`💰 Price: ${transformedProduct.price.currency} ${transformedProduct.price.amount}`)
    console.log(`🏷️  Status: ${transformedProduct.status}`)
    console.log(`🖼️  Images: ${transformedProduct.images.length}`)
    
    return true
  } catch (error) {
    console.error('❌ Data transformation test failed:', error.message)
    return false
  }
}

async function runTests() {
  console.log('🚀 Starting E-commerce API Tests\n')
  
  const tests = [
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Product Service', fn: testProductService },
    { name: 'API Endpoints', fn: testApiEndpoints },
    { name: 'Data Transformation', fn: testDataTransformation }
  ]
  
  let passedTests = 0
  
  for (const test of tests) {
    try {
      const result = await test.fn()
      if (result) {
        passedTests++
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" crashed:`, error.message)
    }
  }
  
  console.log(`\n📊 Test Results: ${passedTests}/${tests.length} tests passed`)
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! E-commerce system should be working.')
  } else {
    console.log('⚠️  Some tests failed. Check the errors above.')
  }
  
  await prisma.$disconnect()
}

// Run the tests
runTests().catch(console.error)
