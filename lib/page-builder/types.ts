// Page Builder Types
// Core types for the custom page builder system

export interface PageBuilderConfig {
  blocks: PageBlock[]
  settings: PageSettings
  metadata?: Record<string, any>
}

export interface PageSettings {
  title: string
  description?: string
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
  ogImage?: string
  customCss?: string
  customJs?: string
  requiresAuth?: boolean
  allowComments?: boolean
}

export interface PageBlock {
  id: string
  type: string
  position: number
  isVisible: boolean
  configuration: BlockConfiguration
  content?: BlockContent
  styling?: BlockStyling
  responsive?: ResponsiveSettings
  animation?: AnimationSettings
  conditions?: DisplayConditions
}

export interface BlockConfiguration {
  [key: string]: any
}

export interface BlockContent {
  [key: string]: any
}

export interface BlockStyling {
  backgroundColor?: string
  textColor?: string
  padding?: SpacingConfig
  margin?: SpacingConfig
  borderRadius?: string
  border?: BorderConfig
  shadow?: string
  customCss?: string
}

export interface SpacingConfig {
  top?: string
  right?: string
  bottom?: string
  left?: string
}

export interface BorderConfig {
  width?: string
  style?: 'solid' | 'dashed' | 'dotted' | 'none'
  color?: string
}

export interface ResponsiveSettings {
  desktop?: Partial<BlockStyling>
  tablet?: Partial<BlockStyling>
  mobile?: Partial<BlockStyling>
  hideOnDesktop?: boolean
  hideOnTablet?: boolean
  hideOnMobile?: boolean
}

export interface AnimationSettings {
  type?: 'fade' | 'slide' | 'scale' | 'bounce' | 'none'
  duration?: number
  delay?: number
  direction?: 'up' | 'down' | 'left' | 'right'
  trigger?: 'scroll' | 'hover' | 'click' | 'load'
}

export interface DisplayConditions {
  userRole?: string[]
  deviceType?: ('desktop' | 'tablet' | 'mobile')[]
  timeRange?: {
    start: Date
    end: Date
  }
  customConditions?: Record<string, any>
}

// Block Type Definitions
export interface BlockTypeDefinition {
  id: string
  name: string
  displayName: string
  description?: string
  category: BlockCategory
  icon?: string
  thumbnail?: string
  defaultConfig: BlockConfiguration
  configSchema: any // JSON Schema
  isActive: boolean
  isSystem: boolean
  version: string
  dependencies?: string[]
  tags?: string[]
}

export type BlockCategory = 'content' | 'ecommerce' | 'marketing' | 'layout' | 'media'

// E-commerce Specific Block Types
export interface ProductGridBlockConfig extends BlockConfiguration {
  productIds?: string[]
  categoryIds?: string[]
  collectionIds?: string[]
  limit: number
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  showPrice: boolean
  showDescription: boolean
  showAddToCart: boolean
  sortBy: 'created' | 'price' | 'title' | 'popularity'
  sortOrder: 'asc' | 'desc'
  filterBy?: {
    inStock?: boolean
    onSale?: boolean
    featured?: boolean
  }
}

// Product Listing Block (combines grid + filters + sort)
export interface ProductListingBlockConfig extends BlockConfiguration {
  title?: string
  description?: string
  showFilters: boolean
  showSort: boolean
  showMobileFilters: boolean
  filtersPosition: 'left' | 'right' | 'top'
  defaultFilters?: {
    category?: string
    color?: string
    size?: string
    priceRange?: { min: number; max: number }
  }
  gridConfig: ProductGridBlockConfig
  filterConfig: {
    showCategories: boolean
    showColors: boolean
    showSizes: boolean
    showPriceRange: boolean
    showBrands: boolean
    showRatings: boolean
    collapsible: boolean
  }
  sortOptions: Array<{
    value: string
    label: string
    enabled: boolean
  }>
}

// Product Details Block
export interface ProductDetailsBlockConfig extends BlockConfiguration {
  showBreadcrumbs: boolean
  showGallery: boolean
  showVariants: boolean
  showDescription: boolean
  showReviews: boolean
  showRelatedProducts: boolean
  showRecommendations: boolean
  showSizeGuide: boolean
  showShippingInfo: boolean
  galleryLayout: 'carousel' | 'grid' | 'stack'
  variantDisplay: 'dropdown' | 'buttons' | 'swatches'
  relatedProductsLimit: number
}

// Cart Block
export interface CartBlockConfig extends BlockConfiguration {
  showContinueShopping: boolean
  showRecommendations: boolean
  showCoupons: boolean
  showShippingCalculator: boolean
  showSummary: boolean
  emptyCartMessage: string
  emptyCartCta: {
    text: string
    url: string
  }
  recommendationsLimit: number
}

// Checkout Block
export interface CheckoutBlockConfig extends BlockConfiguration {
  steps: Array<{
    id: string
    title: string
    enabled: boolean
  }>
  showOrderSummary: boolean
  showProgressIndicator: boolean
  allowGuestCheckout: boolean
  requiredFields: string[]
  paymentMethods: Array<{
    id: string
    name: string
    enabled: boolean
  }>
  shippingMethods: Array<{
    id: string
    name: string
    price: number
    enabled: boolean
  }>
}

// Wishlist Block
export interface WishlistBlockConfig extends BlockConfiguration {
  showMoveToCart: boolean
  showRemove: boolean
  showShare: boolean
  showRecommendations: boolean
  emptyWishlistMessage: string
  emptyWishlistCta: {
    text: string
    url: string
  }
  gridColumns: {
    desktop: number
    tablet: number
    mobile: number
  }
}

// Product Comparison Block
export interface ProductComparisonBlockConfig extends BlockConfiguration {
  maxProducts: number
  showFeatures: string[]
  showPricing: boolean
  showRatings: boolean
  showImages: boolean
  showAddToCart: boolean
  emptyComparisonMessage: string
  emptyComparisonCta: {
    text: string
    url: string
  }
}

// Account Dashboard Block
export interface AccountDashboardBlockConfig extends BlockConfiguration {
  showProfile: boolean
  showOrders: boolean
  showWishlist: boolean
  showAddresses: boolean
  showPaymentMethods: boolean
  showRecentActivity: boolean
  recentOrdersLimit: number
  recentActivityLimit: number
}

// Collection Header Block
export interface CollectionHeaderBlockConfig extends BlockConfiguration {
  title: string
  description?: string
  backgroundImage?: string
  backgroundVideo?: string
  overlay?: {
    enabled: boolean
    color: string
    opacity: number
  }
  showProductCount: boolean
  showBreadcrumbs: boolean
  ctaButton?: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline'
  }
  height: 'auto' | 'small' | 'medium' | 'large' | 'custom'
  customHeight?: string
  alignment: 'left' | 'center' | 'right'
}

// Product Search Block
export interface ProductSearchBlockConfig extends BlockConfiguration {
  placeholder: string
  showSuggestions: boolean
  showCategories: boolean
  showRecentSearches: boolean
  maxSuggestions: number
  searchFilters: string[]
  resultLayout: 'grid' | 'list'
  resultsPerPage: number
}

export interface HeroBlockConfig extends BlockConfiguration {
  title: string
  subtitle?: string
  description?: string
  backgroundImage?: string
  backgroundVideo?: string
  overlay?: {
    enabled: boolean
    color: string
    opacity: number
  }
  ctaButton?: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline'
  }
  alignment: 'left' | 'center' | 'right'
  height: 'auto' | 'viewport' | 'custom'
  customHeight?: string
}

export interface TestimonialBlockConfig extends BlockConfiguration {
  testimonials: Testimonial[]
  layout: 'carousel' | 'grid' | 'single'
  showRating: boolean
  showAvatar: boolean
  showCompany: boolean
  autoplay?: boolean
  autoplaySpeed?: number
}

export interface Testimonial {
  id: string
  name: string
  company?: string
  avatar?: string
  rating?: number
  content: string
}

export interface NewsletterBlockConfig extends BlockConfiguration {
  title: string
  description?: string
  placeholder: string
  buttonText: string
  successMessage: string
  errorMessage: string
  provider: 'mailchimp' | 'klaviyo' | 'custom'
  listId?: string
  tags?: string[]
}

// Page Builder State
export interface PageBuilderState {
  page: PageData
  selectedBlockId: string | null
  isDragging: boolean
  isPreviewMode: boolean
  devicePreview: 'desktop' | 'tablet' | 'mobile'
  history: PageBuilderConfig[]
  historyIndex: number
  isSaving: boolean
  hasUnsavedChanges: boolean,
  canUndo: boolean
  canRedo: boolean
}

export interface PageData {
  id?: string
  title: string
  slug: string
  description?: string
  status: 'draft' | 'published' | 'archived'
  type: 'custom' | 'product' | 'category' | 'home' | 'landing'
  template?: string
  publishedAt?: Date
  createdAt?: Date
  updatedAt?: Date
  blocks: PageBlock[]
  settings: PageSettings
}

// Drag and Drop Types
export interface DragItem {
  id: string
  type: 'block' | 'new-block'
  blockType?: string
  index?: number
}

export interface DropResult {
  dragIndex: number
  hoverIndex: number
}

// Page Builder Actions
export type PageBuilderAction =
  | { type: 'SET_PAGE'; payload: PageData }
  | { type: 'ADD_BLOCK'; payload: { blockType: string; position?: number } }
  | { type: 'UPDATE_BLOCK'; payload: { id: string; updates: Partial<PageBlock> } }
  | { type: 'DELETE_BLOCK'; payload: { id: string } }
  | { type: 'MOVE_BLOCK'; payload: { id: string; newPosition: number } }
  | { type: 'SELECT_BLOCK'; payload: { id: string | null } }
  | { type: 'SET_PREVIEW_MODE'; payload: { enabled: boolean } }
  | { type: 'SET_DEVICE_PREVIEW'; payload: { device: 'desktop' | 'tablet' | 'mobile' } }
  | { type: 'SAVE_PAGE'; payload: { saving: boolean } }
  | { type: 'UNDO' }
  | { type: 'REDO' }

// API Types
export interface CreatePageRequest {
  title: string
  slug: string
  description?: string
  type?: string
  template?: string
  blocks?: Omit<PageBlock, 'id'>[]
  settings?: Partial<PageSettings>
}

export interface UpdatePageRequest extends Partial<CreatePageRequest> {
  id: string
}

export interface PageResponse {
  success: boolean
  data?: PageData
  error?: string
}

export interface PagesResponse {
  success: boolean
  data?: {
    pages: PageData[]
    total: number
    page: number
    limit: number
  }
  error?: string
}

// Template Types
export interface PageTemplate {
  id: string
  name: string
  description?: string
  category: 'ecommerce' | 'landing' | 'content' | 'marketing'
  thumbnail?: string
  configuration: PageBuilderConfig
  isPublic: boolean
  usageCount: number
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface CreateTemplateRequest {
  name: string
  description?: string
  category: string
  configuration: PageBuilderConfig
  isPublic?: boolean
  tags?: string[]
}
