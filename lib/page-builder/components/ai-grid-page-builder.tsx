'use client'

import React, { useState, useCallback } from 'react'
import { usePageBuilder } from '@/stores/use-page-builder'
import { LayoutBuilderProvider } from '@/lib/layout-builder/context'
import { GridLayoutBuilder } from './grid-layout-builder'
import { AILayoutGenerator } from './ai-layout-generator'
import { EnhancedPageBuilderEditor } from './enhanced-page-builder-editor'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Grid, 
  Layout, 
  Wand2, 
  Eye, 
  EyeOff, 
  Save, 
  Settings,
  Layers,
  Smartphone,
  Tablet,
  Monitor,
  Tv,
  ArrowLeft,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AIGridPageBuilderProps {
  initialPage?: any
  initialLayout?: any
  onSave?: (page: any, layout: any) => Promise<void>
  onBack?: () => void
  className?: string
}

type BuilderMode = 'page' | 'layout' | 'hybrid'

export function AIGridPageBuilder({
  initialPage,
  initialLayout,
  onSave,
  onBack,
  className
}: AIGridPageBuilderProps) {
  const [builderMode, setBuilderMode] = useState<BuilderMode>('hybrid')
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [devicePreview, setDevicePreview] = useState<'mobile' | 'tablet' | 'desktop' | 'large'>('desktop')
  const [isSaving, setIsSaving] = useState(false)

  return (
      <LayoutBuilderProvider initialLayout={initialLayout}>
        <AIGridPageBuilderContent
          builderMode={builderMode}
          setBuilderMode={setBuilderMode}
          isPreviewMode={isPreviewMode}
          setIsPreviewMode={setIsPreviewMode}
          devicePreview={devicePreview}
          setDevicePreview={setDevicePreview}
          isSaving={isSaving}
          setIsSaving={setIsSaving}
          onSave={onSave}
          onBack={onBack}
          className={className}
        />
      </LayoutBuilderProvider>
  )
}

interface AIGridPageBuilderContentProps {
  builderMode: BuilderMode
  setBuilderMode: (mode: BuilderMode) => void
  isPreviewMode: boolean
  setIsPreviewMode: (preview: boolean) => void
  devicePreview: 'mobile' | 'tablet' | 'desktop' | 'large'
  setDevicePreview: (device: 'mobile' | 'tablet' | 'desktop' | 'large') => void
  isSaving: boolean
  setIsSaving: (saving: boolean) => void
  onSave?: (page: any, layout: any) => Promise<void>
  onBack?: () => void
  className?: string
}

function AIGridPageBuilderContent({
  builderMode,
  setBuilderMode,
  isPreviewMode,
  setIsPreviewMode,
  devicePreview,
  setDevicePreview,
  isSaving,
  setIsSaving,
  onSave,
  onBack,
  className
}: AIGridPageBuilderContentProps) {
  const pageBuilder = usePageBuilder()

  // Handle save
  const handleSave = useCallback(async () => {
    if (!onSave) return
    
    setIsSaving(true)
    try {
      await onSave(pageBuilder.state.page, null) // Layout would come from layout builder context
      toast.success('Page and layout saved successfully!')
    } catch (error) {
      toast.error('Failed to save page and layout')
      console.error('Save error:', error)
    } finally {
      setIsSaving(false)
    }
  }, [onSave, pageBuilder.state.page])

  // Handle preview toggle
  const handlePreviewToggle = useCallback(() => {
    const newPreviewMode = !isPreviewMode
    setIsPreviewMode(newPreviewMode)
    pageBuilder.setPreviewMode(newPreviewMode)
    
    if (newPreviewMode) {
      toast.success('Preview mode enabled')
    } else {
      toast.success('Edit mode enabled')
    }
  }, [isPreviewMode, setIsPreviewMode, pageBuilder])

  // Handle device change
  const handleDeviceChange = useCallback((device: typeof devicePreview) => {
    setDevicePreview(device)
    pageBuilder.setDevicePreview(device)
    toast.success(`Switched to ${device} preview`)
  }, [setDevicePreview, pageBuilder])

  // Get device icon
  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'mobile': return Smartphone
      case 'tablet': return Tablet
      case 'desktop': return Monitor
      case 'large': return Tv
      default: return Monitor
    }
  }

  return (
    <div className={cn('ai-grid-page-builder h-screen flex flex-col', className)}>
      {/* Header Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          {/* Back Button */}
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}

          {/* Page Title */}
          <div>
            <h1 className="text-lg font-semibold">
              {pageBuilder.state.page.title || 'Untitled Page'}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>{pageBuilder.state.page.blocks.length} blocks</span>
              <Badge variant="outline">{builderMode} mode</Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Builder Mode Toggle */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            <Button
              variant={builderMode === 'page' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setBuilderMode('page')}
              className="h-8 px-3"
            >
              <Layers className="h-4 w-4 mr-1" />
              Page
            </Button>
            <Button
              variant={builderMode === 'layout' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setBuilderMode('layout')}
              className="h-8 px-3"
            >
              <Layout className="h-4 w-4 mr-1" />
              Layout
            </Button>
            <Button
              variant={builderMode === 'hybrid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setBuilderMode('hybrid')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4 mr-1" />
              Hybrid
            </Button>
          </div>

          {/* Device Preview */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            {(['mobile', 'tablet', 'desktop', 'large'] as const).map((device) => {
              const Icon = getDeviceIcon(device)
              return (
                <Button
                  key={device}
                  variant={devicePreview === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleDeviceChange(device)}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>

          {/* Preview Toggle */}
          <Button
            variant={isPreviewMode ? 'default' : 'ghost'}
            size="sm"
            onClick={handlePreviewToggle}
          >
            {isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>

          {/* Save Button */}
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {builderMode === 'page' && (
          <EnhancedPageBuilderEditor
            onSave={async () => {
              await handleSave()
            }}
            onPreview={handlePreviewToggle}
            onBack={onBack}
            className="h-full"
          />
        )}

        {builderMode === 'layout' && (
          <div className="h-full">
            {/* Layout Builder would go here */}
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <Layout className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Layout Builder</h3>
                <p className="text-muted-foreground">
                  Layout builder interface coming soon
                </p>
              </div>
            </div>
          </div>
        )}

        {builderMode === 'hybrid' && (
          <div className="h-full flex">
            {/* AI Panel */}
            {!isPreviewMode && (
              <div className="w-80 border-r bg-background">
                <div className="p-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Wand2 className="h-4 w-4" />
                        <span>AI Assistant</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Tabs defaultValue="layout" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="layout">Layout</TabsTrigger>
                          <TabsTrigger value="content">Content</TabsTrigger>
                        </TabsList>
                        <TabsContent value="layout" className="space-y-4">
                          <AILayoutGenerator
                            onLayoutGenerated={(layout) => {
                              // Apply layout to page builder
                              if (layout.blocks) {
                                pageBuilder.clearBlocks()
                                layout.blocks.forEach((block: any) => {
                                  pageBuilder.addBlock(block)
                                })
                              }
                              toast.success('AI layout applied!')
                            }}
                          />
                        </TabsContent>
                        <TabsContent value="content" className="space-y-4">
                          <div className="text-center text-muted-foreground">
                            <Wand2 className="h-8 w-8 mx-auto mb-2" />
                            <p>AI content generation coming soon</p>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Grid Layout Builder */}
            <div className="flex-1">
              <GridLayoutBuilder
                isPreviewMode={isPreviewMode}
                devicePreview={devicePreview}
                className="h-full"
              />
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/50 text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <span>{pageBuilder.state.page.blocks.length} blocks</span>
          <span>•</span>
          <span>{builderMode} mode</span>
          <span>•</span>
          <span>{devicePreview} preview</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span>
            Last saved: {pageBuilder.state.page.updatedAt 
              ? new Date(pageBuilder.state.page.updatedAt).toLocaleTimeString() 
              : 'Never'
            }
          </span>
        </div>
      </div>
    </div>
  )
}
