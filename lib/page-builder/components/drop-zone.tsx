'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '@/stores/use-page-builder'
import { cn } from '@/lib/utils'
import { Plus } from 'lucide-react'

interface DropZoneProps {
  position: number
  isFirst?: boolean
  isLast?: boolean
  className?: string
}

export function DropZone({ position, isFirst, isLast, className }: DropZoneProps) {
  const { addBlock } = usePageBuilder()
  const [isDragOver, setIsDragOver] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'copy'
    setIsDragOver(true)
  }

  // Handle drag leave
  const handleDragLeave = (e: React.DragEvent) => {
    // Only hide if we're actually leaving the drop zone
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false)
    }
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'))
      
      if (dragData.type === 'new-block') {
        addBlock(dragData.blockType, position)
      } else if (dragData.type === 'existing-block') {
        // Handle moving existing blocks
        // This would be implemented in the block renderer
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  // Handle click to show block selector
  const handleClick = () => {
    setIsVisible(!isVisible)
  }

  return (
    <div
      className={cn(
        'relative transition-all duration-200',
        {
          'h-2': !isDragOver && !isVisible,
          'h-16': isDragOver || isVisible,
          'mt-4': isFirst,
          'mb-4': isLast,
        },
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
    >
      {/* Drop Zone Indicator */}
      <div
        className={cn(
          'absolute inset-0 border-2 border-dashed rounded-lg transition-all duration-200 cursor-pointer',
          {
            'border-transparent hover:border-blue-300': !isDragOver && !isVisible,
            'border-blue-500 bg-blue-50': isDragOver,
            'border-gray-300 bg-gray-50': isVisible && !isDragOver,
          }
        )}
      >
        {/* Drop Zone Content */}
        <div className="flex items-center justify-center h-full">
          {isDragOver ? (
            <div className="flex items-center space-x-2 text-blue-600">
              <Plus className="h-5 w-5" />
              <span className="text-sm font-medium">Drop block here</span>
            </div>
          ) : isVisible ? (
            <div className="flex items-center space-x-2 text-gray-600">
              <Plus className="h-4 w-4" />
              <span className="text-sm">Add block</span>
            </div>
          ) : (
            <div className="opacity-0 hover:opacity-100 transition-opacity">
              <Plus className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
      </div>

      {/* Quick Block Selector */}
      {isVisible && !isDragOver && (
        <QuickBlockSelector
          position={position}
          onClose={() => setIsVisible(false)}
        />
      )}
    </div>
  )
}

// Quick block selector for common blocks
interface QuickBlockSelectorProps {
  position: number
  onClose: () => void
}

function QuickBlockSelector({ position, onClose }: QuickBlockSelectorProps) {
  const { addBlock } = usePageBuilder()

  const quickBlocks = [
    { type: 'hero', name: 'Hero', icon: '🎯' },
    { type: 'product-grid', name: 'Products', icon: '🛍️' },
    { type: 'text', name: 'Text', icon: '📝' },
    { type: 'image', name: 'Image', icon: '🖼️' },
    { type: 'testimonials', name: 'Reviews', icon: '💬' },
    { type: 'newsletter', name: 'Newsletter', icon: '📧' },
  ]

  const handleBlockSelect = (blockType: string) => {
    addBlock(blockType, position)
    onClose()
  }

  // Close on outside click
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element
      if (!target.closest('[data-quick-selector]')) {
        onClose()
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [onClose])

  return (
    <div
      data-quick-selector
      className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white border rounded-lg shadow-lg p-3 z-50"
      onClick={(e) => e.stopPropagation()}
    >
      <div className="grid grid-cols-3 gap-2">
        {quickBlocks.map((block) => (
          <button
            key={block.type}
            onClick={() => handleBlockSelect(block.type)}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <span className="text-2xl mb-1">{block.icon}</span>
            <span className="text-xs font-medium text-gray-700">{block.name}</span>
          </button>
        ))}
      </div>
      
      {/* Arrow pointing up */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
        <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-white"></div>
        <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-200 -mt-px"></div>
      </div>
    </div>
  )
}

// Enhanced drop zone with visual feedback
interface EnhancedDropZoneProps extends DropZoneProps {
  showPreview?: boolean
  previewContent?: React.ReactNode
}

export function EnhancedDropZone({ 
  showPreview, 
  previewContent, 
  ...props 
}: EnhancedDropZoneProps) {
  const [draggedBlockType, setDraggedBlockType] = useState<string | null>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'))
      if (dragData.type === 'new-block') {
        setDraggedBlockType(dragData.blockType)
      }
    } catch {
      // Ignore parsing errors
    }
  }

  const handleDragLeave = () => {
    setDraggedBlockType(null)
  }

  return (
    <div className="relative">
      <DropZone {...props} />
      
      {/* Preview of dragged block */}
      {showPreview && draggedBlockType && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="bg-blue-100 border-2 border-blue-300 border-dashed rounded-lg p-4">
            <div className="text-center text-blue-600">
              <span className="text-sm font-medium">
                {draggedBlockType} block will be added here
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Vertical drop zone for sidebar layouts
export function VerticalDropZone({ position, className }: DropZoneProps) {
  const { addBlock } = usePageBuilder()
  const [isDragOver, setIsDragOver] = useState(false)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = () => {
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'))
      if (dragData.type === 'new-block') {
        addBlock(dragData.blockType, position)
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  return (
    <div
      className={cn(
        'w-2 transition-all duration-200',
        {
          'bg-transparent hover:bg-blue-200': !isDragOver,
          'bg-blue-500': isDragOver,
        },
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    />
  )
}

// Drop zone with animation
export function AnimatedDropZone(props: DropZoneProps) {
  const [isAnimating, setIsAnimating] = useState(false)

  const handleDrop = (e: React.DragEvent) => {
    setIsAnimating(true)
    setTimeout(() => setIsAnimating(false), 300)
  }

  return (
    <div className={cn('transition-all duration-300', {
      'scale-105': isAnimating
    })}>
      <DropZone {...props} />
    </div>
  )
}
