'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '@/stores/use-page-builder'
import { PageBuilderCanvas } from './page-builder-canvas'
import { Button } from '@/components/ui/button'
import {
  PanelLeft,
  PanelRight,
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Settings,
  Code,
  FolderOpen,
  Sparkles,
  MessageSquare,
  Wand2,
  Brain,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { PageSettingsPanel } from './page-settings-panel'
import { PreviewMode } from './preview-mode'
import { CodeEditorPanel } from './code-editor/code-editor-panel'
import { FileExplorer, FileExplorerProvider } from './file-explorer'
import { AIFirstInterface } from './ai-first-interface'
import { EnhancedAIBlockGenerator } from './enhanced-ai-block-generator'

interface PageBuilderEditorProps {
  initialPage?: any
  onSave?: () => Promise<void>
  onPreview?: () => void
  onBack?: () => void
  className?: string
}

export function PageBuilderEditor({
  initialPage: _initialPage,
  onSave: _onSave,
  onPreview,
  onBack: _onBack,
  className
}: PageBuilderEditorProps) {
  const {
    page,
    selectedBlockId,
    isPreviewMode,
    devicePreview,
    hasUnsavedChanges,
    setPreviewMode,
    setDevicePreview,
    undo,
    canUndo,
    canRedo,
    redo,
    addBlock
  } = usePageBuilder()

  const [leftSidebarOpen, setLeftSidebarOpen] = useState(true)
  const [rightSidebarOpen, setRightSidebarOpen] = useState(true)
  const [showPageSettings, setShowPageSettings] = useState(false)
  const [showCodeEditor, setShowCodeEditor] = useState(false)
  const [showFileExplorer, setShowFileExplorer] = useState(false)
  const [leftSidebarTab, setLeftSidebarTab] = useState<'ai' | 'blocks' | 'structure'>('ai')
  const [aiMode, setAiMode] = useState<'primary' | 'secondary'>('primary')
  const [showManualBlocks, setShowManualBlocks] = useState(false)

  // Variables already destructured from usePageBuilder above

  // Handle preview toggle
  const handlePreviewToggle = () => {
    setPreviewMode(!isPreviewMode)
    if (onPreview) {
      onPreview()
    }
  }

  // Handle AI block generation
  const handleBlockGenerated = (block: any) => {
    if (block) {
      // Add the block to the page
      addBlock(block.type)
      // If we need to update configuration, we'd need to do it after adding
      // This would require getting the newly added block ID and updating it
    }
  }

  // Device preview options
  const deviceOptions = [
    { value: 'desktop', icon: Monitor, label: 'Desktop' },
    { value: 'tablet', icon: Tablet, label: 'Tablet' },
    { value: 'mobile', icon: Smartphone, label: 'Mobile' },
  ] as const

  // Show preview mode if enabled
  if (isPreviewMode) {
    return <PreviewMode />
  }

  return (
    <div className={cn('h-full flex flex-col bg-gray-50', className)}>
      {/* Top Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          {/* Left Sidebar Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLeftSidebarOpen(!leftSidebarOpen)}
            className={cn(leftSidebarOpen && 'bg-gray-100')}
          >
            <PanelLeft className="h-4 w-4" />
          </Button>

          {/* Status Indicator */}
          <div className="text-sm text-muted-foreground">
            {hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved'}
          </div>
        </div>

        {/* Center Controls */}
        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <Button
            variant="ghost"
            size="sm"
            onClick={undo}
            disabled={!canUndo}
            title="Undo"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={redo}
            disabled={!canRedo}
            title="Redo"
          >
            <Redo className="h-4 w-4" />
          </Button>

          {/* Device Preview */}
          <div className="flex items-center border rounded-md">
            {deviceOptions.map(({ value, icon: Icon, label }) => (
              <Button
                key={value}
                variant="ghost"
                size="sm"
                onClick={() => setDevicePreview(value)}
                className={cn(
                  'rounded-none border-0',
                  devicePreview === value && 'bg-gray-100'
                )}
                title={label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            ))}
          </div>

          {/* Preview Mode Toggle */}
          <Button
            variant={isPreviewMode ? 'default' : 'ghost'}
            size="sm"
            onClick={handlePreviewToggle}
          >
            {isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>
        </div>

        {/* Right Controls */}
        <div className="flex items-center space-x-2">
          {/* File Explorer */}
          <Sheet open={showFileExplorer} onOpenChange={setShowFileExplorer}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <FolderOpen className="h-4 w-4 mr-2" />
                Files
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[400px] max-w-[90vw]">
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  File Explorer
                </SheetTitle>
              </SheetHeader>
              <FileExplorerProvider>
                <FileExplorer
                  onFileOpen={(file) => {
                    console.log('Open file:', file.name)
                    // Could integrate with code editor here
                  }}
                  className="h-full"
                />
              </FileExplorerProvider>
            </SheetContent>
          </Sheet>

          {/* Code Editor */}
          <Sheet open={showCodeEditor} onOpenChange={setShowCodeEditor}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <Code className="h-4 w-4 mr-2" />
                Code
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[800px] max-w-[90vw]">
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Code Editor
                </SheetTitle>
              </SheetHeader>
              <CodeEditorPanel />
            </SheetContent>
          </Sheet>

          {/* Page Settings */}
          <Sheet open={showPageSettings} onOpenChange={setShowPageSettings}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-96">
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Page Settings
                </SheetTitle>
              </SheetHeader>
              <PageSettingsPanel />
            </SheetContent>
          </Sheet>

          {/* Right Sidebar Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRightSidebarOpen(!rightSidebarOpen)}
            className={cn(rightSidebarOpen && 'bg-gray-100')}
          >
            <PanelRight className="h-4 w-4" />
          </Button>


        </div>
      </div>

      {/* Main Content Area with AI-First Interface */}
      <div className="flex-1 overflow-hidden flex">
        {/* Left Sidebar - AI-First Interface */}
        {leftSidebarOpen && (
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            {/* Sidebar Tabs */}
            <div className="flex border-b border-gray-200">
              <Button
                variant={leftSidebarTab === 'ai' ? 'default' : 'ghost'}
                size="sm"
                className="flex-1 rounded-none"
                onClick={() => setLeftSidebarTab('ai')}
              >
                <Brain className="h-4 w-4 mr-2" />
                AI Assistant
              </Button>
              <Button
                variant={leftSidebarTab === 'blocks' ? 'default' : 'ghost'}
                size="sm"
                className="flex-1 rounded-none"
                onClick={() => setLeftSidebarTab('blocks')}
              >
                <Plus className="h-4 w-4 mr-2" />
                Blocks
              </Button>
            </div>

            {/* Sidebar Content */}
            <div className="flex-1 overflow-hidden">
              {leftSidebarTab === 'ai' && (
                <div className="h-full flex flex-col">
                  <AIFirstInterface className="flex-1" />
                  <div className="p-4 border-t">
                    <EnhancedAIBlockGenerator
                      onBlockGenerated={handleBlockGenerated}
                      className="h-auto"
                    />
                  </div>
                </div>
              )}

              {leftSidebarTab === 'blocks' && (
                <div className="h-full p-4">
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Manual Block Library</h3>
                    <p className="text-xs text-gray-500 mb-4">
                      Drag and drop blocks or use AI assistance above for better results
                    </p>
                  </div>
                  {/* Block Library would go here - simplified for AI-first approach */}
                  <div className="text-center py-8 text-gray-400">
                    <Plus className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Use AI Assistant for better block generation</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 overflow-hidden">
          <PageBuilderCanvas
            devicePreview={devicePreview}
            isPreviewMode={isPreviewMode}
          />
        </div>

        {/* Right Sidebar - Properties Panel */}
        {rightSidebarOpen && (
          <div className="w-80 bg-white border-l border-gray-200">
            <div className="h-full p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-4">Block Properties</h3>
              {/* Properties panel content would go here */}
              <div className="text-center py-8 text-gray-400">
                <Settings className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">Select a block to edit properties</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Bottom Toolbar */}
      <div className="md:hidden bg-white border-t border-gray-200 p-2">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLeftSidebarOpen(!leftSidebarOpen)}
          >
            <PanelLeft className="h-4 w-4" />
          </Button>

          <div className="flex items-center space-x-2">
            {deviceOptions.map(({ value, icon: Icon }) => (
              <Button
                key={value}
                variant="ghost"
                size="sm"
                onClick={() => setDevicePreview(value)}
                className={cn(devicePreview === value && 'bg-gray-100')}
              >
                <Icon className="h-4 w-4" />
              </Button>
            ))}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRightSidebarOpen(!rightSidebarOpen)}
          >
            <PanelRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className="hidden">
        <div className="text-xs text-muted-foreground p-2 bg-gray-100">
          <span className="font-medium">Shortcuts:</span>
          <span className="ml-2">Ctrl+Z (Undo)</span>
          <span className="ml-2">Ctrl+Y (Redo)</span>
          <span className="ml-2">Ctrl+S (Save)</span>
          <span className="ml-2">Ctrl+P (Preview)</span>
        </div>
      </div>
    </div>
  )
}


