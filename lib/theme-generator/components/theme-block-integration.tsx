'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useTheme } from '../theme-context'
import { usePageBuilder } from '@/stores/use-page-builder'
import { ThemeConfig } from '../types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import {
  Palette,
  Wand2,
  Eye,
  EyeOff,
  RefreshCw,
  Check,
  X,
  Sparkles,
  Layers,
  Type,
  Layout,
  Paintbrush,
  Zap,
  Settings,
  Monitor,
  Smartphone,
  Tablet,
  Sun,
  Moon
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface ThemeBlockIntegrationProps {
  className?: string
  blockId?: string
  blockType?: string
  onThemeApplied?: (theme: ThemeConfig) => void
}

export function ThemeBlockIntegration({ 
  className, 
  blockId, 
  blockType,
  onThemeApplied 
}: ThemeBlockIntegrationProps) {
  const { currentTheme, themes, generateTheme } = useTheme()
  const { page, updateBlock } = usePageBuilder()
  const [activeTab, setActiveTab] = useState<'apply' | 'customize' | 'generate'>('apply')
  const [selectedTheme, setSelectedTheme] = useState<ThemeConfig | null>(currentTheme)
  const [isApplying, setIsApplying] = useState(false)
  const [previewMode, setPreviewMode] = useState<'light' | 'dark'>('light')
  const [autoApply, setAutoApply] = useState(false)
  const [themeScope, setThemeScope] = useState<'block' | 'page' | 'global'>('block')

  // Block-specific theme customization
  const [blockThemeOverrides, setBlockThemeOverrides] = useState<any>({})
  const [inheritGlobalTheme, setInheritGlobalTheme] = useState(true)

  // Get current block
  const currentBlock = useMemo(() => {
    if (!blockId || !page?.blocks) return null
    return page.blocks.find((block: any) => block.id === blockId)
  }, [blockId, page?.blocks])

  // Generate theme variables for the block
  const blockThemeVars = useMemo(() => {
    if (!selectedTheme) return {}

    const baseVars = {
      '--theme-primary': selectedTheme.colors?.primary?.DEFAULT || '#3b82f6',
      '--theme-secondary': selectedTheme.colors?.secondary?.DEFAULT || '#6b7280',
      '--theme-accent': selectedTheme.colors?.accent?.DEFAULT || '#8b5cf6',
      '--theme-background': selectedTheme.colors?.background || '#ffffff',
      '--theme-foreground': selectedTheme.colors?.foreground || '#1f2937',
      '--theme-border': selectedTheme.colors?.border || '#e5e7eb',
      '--theme-radius': selectedTheme.borderRadius?.DEFAULT || '0.5rem',
      '--theme-shadow': selectedTheme.boxShadow?.DEFAULT || '0 1px 3px 0 rgb(0 0 0 / 0.1)',
    }

    // Apply block-specific overrides
    return { ...baseVars, ...blockThemeOverrides }
  }, [selectedTheme, blockThemeOverrides])

  // Apply theme to block
  const applyThemeToBlock = async (theme: ThemeConfig, scope: 'block' | 'page' | 'global' = 'block') => {
    if (!blockId && scope === 'block') return

    setIsApplying(true)
    try {
      if (scope === 'block' && blockId) {
        // Apply theme to specific block
        const themeConfig = {
          themeId: theme.id,
          themeVars: blockThemeVars,
          inheritGlobal: inheritGlobalTheme,
          overrides: blockThemeOverrides
        }

        await updateBlock(blockId, {
          theme: themeConfig
        })

        toast.success(`Theme applied to ${blockType || 'block'}`)
      } else if (scope === 'page') {
        // Apply theme to entire page
        // This would need to be implemented in the page builder context
        toast.success('Theme applied to page')
      } else if (scope === 'global') {
        // Apply theme globally
        // This would use the theme context setTheme method
        toast.success('Theme applied globally')
      }

      if (onThemeApplied) {
        onThemeApplied(theme)
      }
    } catch (error) {
      console.error('Theme application error:', error)
      toast.error('Failed to apply theme')
    } finally {
      setIsApplying(false)
    }
  }

  // Auto-apply theme when selection changes
  useEffect(() => {
    if (autoApply && selectedTheme) {
      applyThemeToBlock(selectedTheme, themeScope)
    }
  }, [selectedTheme, autoApply, themeScope])

  // Generate contextual theme suggestions
  const generateContextualTheme = async () => {
    setIsApplying(true)
    try {
      // Generate theme based on block type and context
      const contextPrompt = getContextPrompt(blockType)
      
      // This would call the AI theme generation with context
      const newTheme = await generateTheme({
        baseColor: '#3b82f6',
        style: 'modern',
        contrast: 'medium',
        saturation: 'normal',
        borderRadius: 'rounded',
        fontPairing: 'modern',
        spacing: 'normal'
      })

      setSelectedTheme(newTheme)
      
      if (autoApply) {
        await applyThemeToBlock(newTheme, themeScope)
      }

      toast.success('Contextual theme generated!')
    } catch (error) {
      console.error('Contextual theme generation error:', error)
      toast.error('Failed to generate contextual theme')
    } finally {
      setIsApplying(false)
    }
  }

  const getContextPrompt = (blockType?: string): string => {
    switch (blockType) {
      case 'hero':
        return 'Create a bold, attention-grabbing theme suitable for hero sections with high contrast and strong call-to-action elements'
      case 'testimonials':
        return 'Design a trustworthy, professional theme that emphasizes credibility and social proof'
      case 'products':
        return 'Generate an e-commerce optimized theme with clear product focus and conversion-friendly colors'
      case 'contact':
        return 'Create a clean, approachable theme that encourages user interaction and form completion'
      case 'features':
        return 'Design a modern, informative theme that clearly presents features and benefits'
      default:
        return 'Create a versatile, modern theme suitable for general content blocks'
    }
  }

  const renderThemePreview = (theme: ThemeConfig) => {
    const isSelected = selectedTheme?.id === theme.id
    
    return (
      <Card 
        key={theme.id}
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          isSelected && 'ring-2 ring-purple-500 ring-offset-2'
        )}
        onClick={() => setSelectedTheme(theme)}
      >
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            {/* Color Preview */}
            <div className="flex gap-1">
              {theme.colors?.primary?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.primary.DEFAULT }}
                />
              )}
              {theme.colors?.secondary?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.secondary.DEFAULT }}
                />
              )}
              {theme.colors?.accent?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.accent.DEFAULT }}
                />
              )}
            </div>

            {/* Theme Info */}
            <div className="flex-1">
              <h4 className="font-medium text-sm">{theme.name}</h4>
              {theme.description && (
                <p className="text-xs text-muted-foreground truncate">
                  {theme.description}
                </p>
              )}
            </div>

            {/* Apply Button */}
            <Button
              size="sm"
              variant={isSelected ? 'default' : 'outline'}
              onClick={(e) => {
                e.stopPropagation()
                applyThemeToBlock(theme, themeScope)
              }}
              disabled={isApplying}
              className="text-xs h-6 px-2"
            >
              {isApplying ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : isSelected ? (
                <Check className="h-3 w-3" />
              ) : (
                'Apply'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base">
            <Palette className="h-4 w-4 text-purple-600" />
            Block Theming
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {blockType || 'Block'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={generateContextualTheme}
              disabled={isApplying}
            >
              <Sparkles className="h-3 w-3 mr-1" />
              AI Theme
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Settings */}
        <div className="p-4 border-b space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm">Auto-apply themes</Label>
            <Switch checked={autoApply} onCheckedChange={setAutoApply} />
          </div>
          
          <div className="flex items-center justify-between">
            <Label className="text-sm">Inherit global theme</Label>
            <Switch checked={inheritGlobalTheme} onCheckedChange={setInheritGlobalTheme} />
          </div>

          <div>
            <Label className="text-sm">Theme scope</Label>
            <Select value={themeScope} onValueChange={(value: any) => setThemeScope(value)}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="block">This Block Only</SelectItem>
                <SelectItem value="page">Entire Page</SelectItem>
                <SelectItem value="global">Global Theme</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
            <TabsTrigger value="apply" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Apply
            </TabsTrigger>
            <TabsTrigger value="customize" className="text-xs">
              <Wand2 className="h-3 w-3 mr-1" />
              Customize
            </TabsTrigger>
            <TabsTrigger value="generate" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              Generate
            </TabsTrigger>
          </TabsList>

          {/* Apply Tab */}
          <TabsContent value="apply" className="flex-1 mt-4">
            <ScrollArea className="h-full px-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium">Available Themes</h4>
                  <div className="flex items-center gap-1">
                    <Button
                      variant={previewMode === 'light' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('light')}
                      className="h-6 w-6 p-0"
                    >
                      <Sun className="h-3 w-3" />
                    </Button>
                    <Button
                      variant={previewMode === 'dark' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('dark')}
                      className="h-6 w-6 p-0"
                    >
                      <Moon className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {themes.length === 0 ? (
                  <div className="text-center py-8">
                    <Palette className="h-8 w-8 mx-auto mb-3 text-gray-400" />
                    <p className="text-sm text-muted-foreground">
                      No themes available. Create a theme first.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {themes.map(renderThemePreview)}
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Customize Tab */}
          <TabsContent value="customize" className="flex-1 mt-4">
            <ScrollArea className="h-full px-4">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Block Theme Overrides</h4>
                
                {selectedTheme ? (
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm">Primary Color</Label>
                      <div className="flex gap-2 mt-1">
                        <input
                          type="color"
                          value={blockThemeOverrides['--theme-primary'] || selectedTheme.colors?.primary?.DEFAULT || '#3b82f6'}
                          onChange={(e) => setBlockThemeOverrides(prev => ({
                            ...prev,
                            '--theme-primary': e.target.value
                          }))}
                          className="w-8 h-8 rounded border"
                        />
                        <div className="text-xs text-muted-foreground self-center">
                          Override primary color for this block
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm">Background Color</Label>
                      <div className="flex gap-2 mt-1">
                        <input
                          type="color"
                          value={blockThemeOverrides['--theme-background'] || selectedTheme.colors?.background || '#ffffff'}
                          onChange={(e) => setBlockThemeOverrides(prev => ({
                            ...prev,
                            '--theme-background': e.target.value
                          }))}
                          className="w-8 h-8 rounded border"
                        />
                        <div className="text-xs text-muted-foreground self-center">
                          Override background color for this block
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm">Border Radius</Label>
                      <input
                        type="text"
                        value={blockThemeOverrides['--theme-radius'] || selectedTheme.borderRadius?.DEFAULT || '0.5rem'}
                        onChange={(e) => setBlockThemeOverrides(prev => ({
                          ...prev,
                          '--theme-radius': e.target.value
                        }))}
                        className="w-full mt-1 px-3 py-1 text-sm border rounded"
                        placeholder="e.g., 0.5rem, 8px, 0"
                      />
                    </div>

                    <Button
                      onClick={() => applyThemeToBlock(selectedTheme, themeScope)}
                      disabled={isApplying}
                      className="w-full"
                    >
                      {isApplying ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Applying...
                        </>
                      ) : (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Apply Customizations
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Wand2 className="h-8 w-8 mx-auto mb-3 text-gray-400" />
                    <p className="text-sm text-muted-foreground">
                      Select a theme to customize
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Generate Tab */}
          <TabsContent value="generate" className="flex-1 mt-4">
            <ScrollArea className="h-full px-4">
              <div className="space-y-4">
                <div className="text-center py-6">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                  <h3 className="font-medium mb-2">AI Theme Generation</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Generate themes optimized for {blockType || 'this block type'}
                  </p>
                  
                  <Button
                    onClick={generateContextualTheme}
                    disabled={isApplying}
                    className="w-full"
                  >
                    {isApplying ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Generate Contextual Theme
                      </>
                    )}
                  </Button>
                </div>

                {/* Quick Theme Options */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Quick Options</h4>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      // Generate high contrast theme
                      generateTheme({
                        baseColor: '#000000',
                        style: 'bold',
                        contrast: 'high',
                        saturation: 'normal',
                        borderRadius: 'sharp',
                        fontPairing: 'modern',
                        spacing: 'normal'
                      }).then(setSelectedTheme)
                    }}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    High Contrast
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      // Generate minimal theme
                      generateTheme({
                        baseColor: '#6b7280',
                        style: 'minimal',
                        contrast: 'low',
                        saturation: 'muted',
                        borderRadius: 'rounded',
                        fontPairing: 'classic',
                        spacing: 'spacious'
                      }).then(setSelectedTheme)
                    }}
                  >
                    <Layout className="h-4 w-4 mr-2" />
                    Minimal Clean
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      // Generate colorful theme
                      generateTheme({
                        baseColor: '#8b5cf6',
                        style: 'playful',
                        contrast: 'medium',
                        saturation: 'vibrant',
                        borderRadius: 'pill',
                        fontPairing: 'creative',
                        spacing: 'normal'
                      }).then(setSelectedTheme)
                    }}
                  >
                    <Paintbrush className="h-4 w-4 mr-2" />
                    Colorful & Fun
                  </Button>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
