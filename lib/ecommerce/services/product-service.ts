// Product Management Service
// Handles all product-related operations including CRUD, search, and analytics

import { PrismaClient } from '@prisma/client'
import { 
  Product, 
  CreateProductInput, 
  UpdateProductInput, 
  ProductSearchParams,
  ProductFilters,
  ProductSortOptions,
  PaginatedResponse,
  ApiResponse,
  NotFoundError,
  ValidationError
} from '../types'
import { prisma } from '../config/database'
import { DEFAULT_CURRENCY, PAGINATION, PRODUCT_STATUSES } from '../config/constants'

export class ProductService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Create a new product
   */
  async createProduct(input: CreateProductInput): Promise<ApiResponse<Product>> {
    try {
      // Validate input
      this.validateProductInput(input)

      // Generate slug if not provided
      const slug = input.slug || this.generateSlug(input.title)

      // Check if slug already exists
      const existingProduct = await this.db.product.findUnique({
        where: { slug }
      })

      if (existingProduct) {
        throw new ValidationError(`Product with slug '${slug}' already exists`)
      }

      // Create product with relations
      const product = await this.db.product.create({
        data: {
          title: input.title,
          slug,
          description: input.description,
          descriptionHtml: input.descriptionHtml,
          vendor: input.vendor,
          productType: input.productType,
          handle: slug, // Use slug as handle
          status: input.status || PRODUCT_STATUSES.DRAFT,
          publishedAt: input.status === PRODUCT_STATUSES.ACTIVE ? new Date() : null,
          price: input.price.amount,
          compareAtPrice: input.compareAtPrice?.amount,
          costPerItem: input.costPerItem?.amount,
          currency: input.price.currency || DEFAULT_CURRENCY,
          trackQuantity: input.trackQuantity ?? true,
          continueSellingWhenOutOfStock: input.continueSellingWhenOutOfStock ?? false,
          inventoryQuantity: input.inventoryQuantity || 0,
          weight: input.weight,
          weightUnit: input.weightUnit,
          dimensionLength: input.dimensions?.length,
          dimensionWidth: input.dimensions?.width,
          dimensionHeight: input.dimensions?.height,
          dimensionUnit: input.dimensions?.unit,
          hasVariants: (input.variants?.length || 0) > 0,
          seoTitle: input.seo?.title,
          seoDescription: input.seo?.description,
          seoKeywords: input.seo?.keywords || [],
          metafields: input.metafields,
          isGiftCard: input.isGiftCard ?? false,
          requiresShipping: input.requiresShipping ?? true,
          isTaxable: input.isTaxable ?? true,
          isVisible: input.isVisible ?? true,
          isAvailable: true,
          availableForSale: true,
          
          // Create images
          images: input.images ? {
            create: input.images.map((image, index) => ({
              url: image.url,
              altText: image.altText,
              position: index,
              width: image.width,
              height: image.height
            }))
          } : undefined,

          // Create variants
          variants: input.variants ? {
            create: input.variants.map((variant, index) => ({
              sku: variant.sku,
              title: variant.title,
              price: variant.price.amount,
              compareAtPrice: variant.compareAtPrice?.amount,
              currency: variant.price.currency || DEFAULT_CURRENCY,
              weight: variant.weight,
              weightUnit: variant.weightUnit,
              inventoryQuantity: variant.inventoryQuantity || 0,
              inventoryPolicy: variant.inventoryPolicy || 'deny',
              fulfillmentService: variant.fulfillmentService || 'manual',
              inventoryManagement: variant.inventoryManagement ?? true,
              available: variant.available ?? true,
              position: index,
              
              // Create variant options
              options: variant.options ? {
                create: variant.options.map(option => ({
                  name: option.name,
                  value: option.value
                }))
              } : undefined
            }))
          } : undefined,

          // Create options
          options: input.options ? {
            create: input.options.map(option => ({
              name: option.name,
              position: option.position,
              values: option.values
            }))
          } : undefined
        },
        include: {
          images: true,
          variants: {
            include: {
              options: true
            }
          },
          options: true,
          categories: {
            include: {
              category: true
            }
          },
          tags: {
            include: {
              tag: true
            }
          },
          collections: {
            include: {
              collection: true
            }
          }
        }
      })

      // Handle category associations
      if (input.categoryIds && input.categoryIds.length > 0) {
        await this.db.productCategoryRelation.createMany({
          data: input.categoryIds.map(categoryId => ({
            productId: product.id,
            categoryId
          }))
        })
      }

      // Handle tag associations
      if (input.tagIds && input.tagIds.length > 0) {
        await this.db.productTagRelation.createMany({
          data: input.tagIds.map(tagId => ({
            productId: product.id,
            tagId
          }))
        })
      }

      // Handle collection associations
      if (input.collectionIds && input.collectionIds.length > 0) {
        await this.db.productCollectionRelation.createMany({
          data: input.collectionIds.map((collectionId, index) => ({
            productId: product.id,
            collectionId,
            position: index
          }))
        })
      }

      // Fetch the complete product with all relations
      const completeProduct = await this.getProductById(product.id)

      return {
        success: true,
        data: completeProduct.data!
      }
    } catch (error) {
      console.error('Error creating product:', error)
      return {
        success: false,
        error: {
          code: error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create product'
        }
      }
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<ApiResponse<Product>> {
    try {
      const product = await this.db.product.findUnique({
        where: { id },
        include: {
          images: {
            orderBy: { position: 'asc' }
          },
          variants: {
            include: {
              options: true
            },
            orderBy: { position: 'asc' }
          },
          options: {
            orderBy: { position: 'asc' }
          },
          categories: {
            include: {
              category: true
            }
          },
          tags: {
            include: {
              tag: true
            }
          },
          collections: {
            include: {
              collection: true
            }
          },
          reviews: {
            where: { isApproved: true },
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        }
      })

      if (!product) {
        throw new NotFoundError('Product', id)
      }

      return {
        success: true,
        data: this.transformProductData(product)
      }
    } catch (error) {
      console.error('Error getting product:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get product'
        }
      }
    }
  }

  /**
   * Get product by slug
   */
  async getProductBySlug(slug: string): Promise<ApiResponse<Product>> {
    try {
      const product = await this.db.product.findUnique({
        where: { slug },
        include: {
          images: {
            orderBy: { position: 'asc' }
          },
          variants: {
            include: {
              options: true
            },
            orderBy: { position: 'asc' }
          },
          options: {
            orderBy: { position: 'asc' }
          },
          categories: {
            include: {
              category: true
            }
          },
          tags: {
            include: {
              tag: true
            }
          },
          collections: {
            include: {
              collection: true
            }
          },
          reviews: {
            where: { isApproved: true },
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        }
      })

      if (!product) {
        throw new NotFoundError('Product', slug)
      }

      return {
        success: true,
        data: this.transformProductData(product)
      }
    } catch (error) {
      console.error('Error getting product by slug:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get product'
        }
      }
    }
  }

  /**
   * Search and filter products
   */
  async searchProducts(params: ProductSearchParams = {}): Promise<ApiResponse<PaginatedResponse<Product>>> {
    try {
      const {
        query,
        filters = {},
        sort = { field: 'createdAt', direction: 'desc' },
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = params

      // Build where clause
      const where = this.buildProductWhereClause(query, filters)

      // Build order by clause
      const orderBy = this.buildProductOrderBy(sort)

      // Calculate pagination
      const skip = (page - 1) * Math.min(limit, PAGINATION.MAX_LIMIT)
      const take = Math.min(limit, PAGINATION.MAX_LIMIT)

      // Execute queries
      const [products, total] = await Promise.all([
        this.db.product.findMany({
          where,
          include: {
            images: {
              orderBy: { position: 'asc' },
              take: 1 // Only get the first image for list view
            },
            categories: {
              include: {
                category: true
              }
            },
            tags: {
              include: {
                tag: true
              }
            }
          },
          orderBy,
          skip,
          take
        }),
        this.db.product.count({ where })
      ])

      const totalPages = Math.ceil(total / take)

      return {
        success: true,
        data: {
          data: products.map((product: any) => this.transformProductData(product)),
          pagination: {
            page,
            limit: take,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      }
    } catch (error) {
      console.error('Error searching products:', error)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to search products'
        }
      }
    }
  }

  // Private helper methods
  private validateProductInput(input: CreateProductInput): void {
    if (!input.title?.trim()) {
      throw new ValidationError('Product title is required')
    }

    if (!input.description?.trim()) {
      throw new ValidationError('Product description is required')
    }

    if (!input.price || input.price.amount <= 0) {
      throw new ValidationError('Product price must be greater than 0')
    }

    if (input.compareAtPrice && input.compareAtPrice.amount <= input.price.amount) {
      throw new ValidationError('Compare at price must be greater than regular price')
    }
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }

  private buildProductWhereClause(query?: string, filters: ProductFilters = {}) {
    const where: any = {}

    // Text search
    if (query) {
      where.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { vendor: { contains: query, mode: 'insensitive' } },
        { productType: { contains: query, mode: 'insensitive' } }
      ]
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status }
    }

    // Visibility filter
    if (filters.isVisible !== undefined) {
      where.isVisible = filters.isVisible
    }

    // Price range filter
    if (filters.priceRange) {
      where.price = {}
      if (filters.priceRange.min !== undefined) {
        where.price.gte = filters.priceRange.min
      }
      if (filters.priceRange.max !== undefined) {
        where.price.lte = filters.priceRange.max
      }
    }

    // Stock filter
    if (filters.inStock !== undefined) {
      if (filters.inStock) {
        where.inventoryQuantity = { gt: 0 }
      } else {
        where.inventoryQuantity = { lte: 0 }
      }
    }

    // Sale filter
    if (filters.onSale !== undefined && filters.onSale) {
      where.compareAtPrice = { not: null }
    }

    // Vendor filter
    if (filters.vendor) {
      where.vendor = { contains: filters.vendor, mode: 'insensitive' }
    }

    // Product type filter
    if (filters.productType) {
      where.productType = { contains: filters.productType, mode: 'insensitive' }
    }

    // Category filter
    if (filters.categoryIds && filters.categoryIds.length > 0) {
      where.categories = {
        some: {
          categoryId: { in: filters.categoryIds }
        }
      }
    }

    // Tag filter
    if (filters.tagIds && filters.tagIds.length > 0) {
      where.tags = {
        some: {
          tagId: { in: filters.tagIds }
        }
      }
    }

    // Collection filter
    if (filters.collectionIds && filters.collectionIds.length > 0) {
      where.collections = {
        some: {
          collectionId: { in: filters.collectionIds }
        }
      }
    }

    return where
  }

  private buildProductOrderBy(sort: ProductSortOptions) {
    const { field, direction } = sort

    switch (field) {
      case 'title':
        return { title: direction }
      case 'price':
        return { price: direction }
      case 'inventoryQuantity':
        return { inventoryQuantity: direction }
      case 'averageRating':
        return { averageRating: direction }
      case 'updatedAt':
        return { updatedAt: direction }
      case 'createdAt':
      default:
        return { createdAt: direction }
    }
  }

  /**
   * Update an existing product
   */
  async updateProduct(input: UpdateProductInput): Promise<ApiResponse<Product>> {
    try {
      // Check if product exists
      const existingProduct = await this.db.product.findUnique({
        where: { id: input.id }
      })

      if (!existingProduct) {
        throw new NotFoundError('Product', input.id)
      }

      // Validate input
      if (input.title || input.description || input.price) {
        this.validateProductInput({
          title: input.title || existingProduct.title,
          description: input.description || existingProduct.description,
          price: input.price || { amount: Number(existingProduct.price), currency: existingProduct.currency }
        } as CreateProductInput)
      }

      // Generate new slug if title changed
      let slug = existingProduct.slug
      if (input.title && input.title !== existingProduct.title) {
        slug = this.generateSlug(input.title)

        // Check if new slug already exists
        const slugExists = await this.db.product.findFirst({
          where: {
            slug,
            id: { not: input.id }
          }
        })

        if (slugExists) {
          slug = `${slug}-${Date.now()}`
        }
      }

      // Update product
      const updatedProduct = await this.db.product.update({
        where: { id: input.id },
        data: {
          title: input.title,
          slug: input.title ? slug : undefined,
          description: input.description,
          descriptionHtml: input.descriptionHtml,
          vendor: input.vendor,
          productType: input.productType,
          handle: input.title ? slug : undefined,
          status: input.status,
          publishedAt: input.status === PRODUCT_STATUSES.ACTIVE && !existingProduct.publishedAt ? new Date() : undefined,
          price: input.price?.amount,
          compareAtPrice: input.compareAtPrice?.amount,
          costPerItem: input.costPerItem?.amount,
          currency: input.price?.currency,
          trackQuantity: input.trackQuantity,
          continueSellingWhenOutOfStock: input.continueSellingWhenOutOfStock,
          inventoryQuantity: input.inventoryQuantity,
          weight: input.weight,
          weightUnit: input.weightUnit,
          dimensionLength: input.dimensions?.length,
          dimensionWidth: input.dimensions?.width,
          dimensionHeight: input.dimensions?.height,
          dimensionUnit: input.dimensions?.unit,
          seoTitle: input.seo?.title,
          seoDescription: input.seo?.description,
          seoKeywords: input.seo?.keywords,
          metafields: input.metafields,
          isGiftCard: input.isGiftCard,
          requiresShipping: input.requiresShipping,
          isTaxable: input.isTaxable,
          isVisible: input.isVisible
        },
        include: {
          images: true,
          variants: {
            include: {
              options: true
            }
          },
          options: true,
          categories: {
            include: {
              category: true
            }
          },
          tags: {
            include: {
              tag: true
            }
          },
          collections: {
            include: {
              collection: true
            }
          }
        }
      })

      return {
        success: true,
        data: this.transformProductData(updatedProduct)
      }
    } catch (error) {
      console.error('Error updating product:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' :
                error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update product'
        }
      }
    }
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if product exists
      const existingProduct = await this.db.product.findUnique({
        where: { id }
      })

      if (!existingProduct) {
        throw new NotFoundError('Product', id)
      }

      // Delete product (cascade will handle related records)
      await this.db.product.delete({
        where: { id }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete product'
        }
      }
    }
  }

  /**
   * Get related products
   */
  async getRelatedProducts(productId: string, limit: number = 4): Promise<ApiResponse<Product[]>> {
    try {
      const product = await this.db.product.findUnique({
        where: { id: productId },
        include: {
          categories: {
            include: {
              category: true
            }
          }
        }
      })

      if (!product) {
        throw new NotFoundError('Product', productId)
      }

      const categoryIds = product.categories.map((rel: any) => rel.categoryId)

      const relatedProducts = await this.db.product.findMany({
        where: {
          id: { not: productId },
          isVisible: true,
          status: PRODUCT_STATUSES.ACTIVE,
          categories: {
            some: {
              categoryId: { in: categoryIds }
            }
          }
        },
        include: {
          images: {
            orderBy: { position: 'asc' },
            take: 1
          },
          categories: {
            include: {
              category: true
            }
          }
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      })

      return {
        success: true,
        data: relatedProducts.map((p: any) => this.transformProductData(p))
      }
    } catch (error) {
      console.error('Error getting related products:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get related products'
        }
      }
    }
  }

  /**
   * Update product inventory
   */
  async updateInventory(productId: string, quantity: number, variantId?: string): Promise<ApiResponse<boolean>> {
    try {
      if (variantId) {
        // Update variant inventory
        const variant = await this.db.productVariant.findUnique({
          where: { id: variantId }
        })

        if (!variant || variant.productId !== productId) {
          throw new NotFoundError('Product variant', variantId)
        }

        await this.db.productVariant.update({
          where: { id: variantId },
          data: { inventoryQuantity: quantity }
        })
      } else {
        // Update product inventory
        const product = await this.db.product.findUnique({
          where: { id: productId }
        })

        if (!product) {
          throw new NotFoundError('Product', productId)
        }

        await this.db.product.update({
          where: { id: productId },
          data: { inventoryQuantity: quantity }
        })
      }

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Error updating inventory:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update inventory'
        }
      }
    }
  }

  private transformProductData(product: any): Product {
    // Transform the Prisma product data to match our Product interface
    // Provide safe defaults for all required fields
    const currency = product.currency || 'ZAR'

    return {
      id: product.id,
      title: product.title || '',
      slug: product.slug || '',
      description: product.description || '',
      descriptionHtml: product.descriptionHtml || undefined,
      vendor: product.vendor || undefined,
      productType: product.productType || undefined,
      handle: product.handle || product.slug || '',
      status: product.status || 'draft',
      publishedAt: product.publishedAt || undefined,
      price: {
        amount: Number(product.price) || 0,
        currency
      },
      compareAtPrice: product.compareAtPrice ? {
        amount: Number(product.compareAtPrice),
        currency
      } : undefined,
      costPerItem: product.costPerItem ? {
        amount: Number(product.costPerItem),
        currency
      } : undefined,
      trackQuantity: product.trackQuantity ?? true,
      continueSellingWhenOutOfStock: product.continueSellingWhenOutOfStock ?? false,
      inventoryQuantity: product.inventoryQuantity || 0,
      weight: product.weight ? Number(product.weight) : undefined,
      weightUnit: product.weightUnit || undefined,
      dimensions: product.dimensionLength ? {
        length: Number(product.dimensionLength),
        width: Number(product.dimensionWidth),
        height: Number(product.dimensionHeight),
        unit: product.dimensionUnit || 'cm'
      } : undefined,
      images: product.images?.map((image: any) => ({
        id: image.id,
        url: image.url || '',
        altText: image.altText || '',
        position: image.position || 0,
        width: image.width || undefined,
        height: image.height || undefined
      })) || [],
      featuredImage: product.images?.[0] ? {
        id: product.images[0].id,
        url: product.images[0].url || '',
        altText: product.images[0].altText || '',
        position: product.images[0].position || 0,
        width: product.images[0].width || undefined,
        height: product.images[0].height || undefined
      } : undefined,
      hasVariants: product.hasVariants ?? false,
      variants: product.variants?.map((variant: any) => ({
        id: variant.id,
        productId: variant.productId,
        sku: variant.sku || '',
        title: variant.title || '',
        price: {
          amount: Number(variant.price) || 0,
          currency: variant.currency || currency
        },
        compareAtPrice: variant.compareAtPrice ? {
          amount: Number(variant.compareAtPrice),
          currency: variant.currency || currency
        } : undefined,
        weight: variant.weight ? Number(variant.weight) : undefined,
        weightUnit: variant.weightUnit || undefined,
        inventoryQuantity: variant.inventoryQuantity || 0,
        inventoryPolicy: variant.inventoryPolicy || 'deny',
        fulfillmentService: variant.fulfillmentService || 'manual',
        inventoryManagement: variant.inventoryManagement ?? true,
        options: variant.options?.map((option: any) => ({
          name: option.name || '',
          value: option.value || ''
        })) || [],
        available: variant.available ?? true,
        createdAt: variant.createdAt,
        updatedAt: variant.updatedAt
      })) || [],
      options: product.options?.map((option: any) => ({
        id: option.id,
        name: option.name || '',
        position: option.position || 0,
        values: option.values || []
      })) || [],
      categories: product.categories?.map((rel: any) => rel.category).filter(Boolean) || [],
      tags: product.tags?.map((rel: any) => rel.tag).filter(Boolean) || [],
      collections: product.collections?.map((rel: any) => rel.collection).filter(Boolean) || [],
      seo: {
        title: product.seoTitle || undefined,
        description: product.seoDescription || undefined,
        keywords: product.seoKeywords || []
      },
      reviews: product.reviews || [],
      averageRating: product.averageRating ? Number(product.averageRating) : undefined,
      reviewCount: product.reviewCount || 0,
      metafields: product.metafields || {},
      isGiftCard: product.isGiftCard ?? false,
      requiresShipping: product.requiresShipping ?? true,
      isTaxable: product.isTaxable ?? true,
      isVisible: product.isVisible ?? true,
      isAvailable: product.isAvailable ?? true,
      availableForSale: product.availableForSale ?? true,
      relatedProductIds: product.relatedProductIds || [],
      crossSellProductIds: product.crossSellProductIds || [],
      upSellProductIds: product.upSellProductIds || [],
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }
  }

  /**
   * Bulk update product status
   */
  async bulkUpdateStatus(productIds: string[], status: string): Promise<ApiResponse<{ updatedCount: number }>> {
    try {
      // Validate status
      if (!Object.values(PRODUCT_STATUSES).includes(status as any)) {
        throw new ValidationError(`Invalid status: ${status}`)
      }

      // Update products
      const result = await this.db.product.updateMany({
        where: {
          id: { in: productIds }
        },
        data: {
          status,
          publishedAt: status === PRODUCT_STATUSES.ACTIVE ? new Date() : undefined
        }
      })

      return {
        success: true,
        data: { updatedCount: result.count }
      }
    } catch (error) {
      console.error('Error bulk updating product status:', error)
      return {
        success: false,
        error: {
          code: error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to bulk update product status'
        }
      }
    }
  }

  /**
   * Bulk delete products
   */
  async bulkDeleteProducts(
    productIds: string[],
    options: { force?: boolean; reason?: string } = {}
  ): Promise<ApiResponse<{ deletedCount: number; skippedCount: number; errors: string[] }>> {
    try {
      const { force = false } = options
      let deletedCount = 0
      let skippedCount = 0
      const errors: string[] = []

      for (const productId of productIds) {
        try {
          // Check if product has orders (if not forcing)
          if (!force) {
            // This would check for existing orders
            // For now, we'll assume it's safe to delete
          }

          // Delete related records first
          await this.db.$transaction(async (tx) => {
            // Delete product relations
            await tx.productCategoryRelation.deleteMany({
              where: { productId }
            })
            await tx.productTagRelation.deleteMany({
              where: { productId }
            })
            await tx.productCollectionRelation.deleteMany({
              where: { productId }
            })

            // Delete variants and their options
            await tx.productVariantOption.deleteMany({
              where: {
                variant: {
                  productId
                }
              }
            })
            await tx.productVariant.deleteMany({
              where: { productId }
            })

            // Delete product options
            await tx.productOption.deleteMany({
              where: { productId }
            })

            // Delete images
            await tx.productImage.deleteMany({
              where: { productId }
            })

            // Delete the product
            await tx.product.delete({
              where: { id: productId }
            })
          })

          deletedCount++
        } catch (error) {
          skippedCount++
          errors.push(`Failed to delete product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      return {
        success: true,
        data: { deletedCount, skippedCount, errors }
      }
    } catch (error) {
      console.error('Error bulk deleting products:', error)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to bulk delete products'
        }
      }
    }
  }

  /**
   * Duplicate a product
   */
  async duplicateProduct(
    productId: string,
    options: {
      titleSuffix?: string
      includeInventory?: boolean
      includeImages?: boolean
      includeVariants?: boolean
      includeCategories?: boolean
      includeCollections?: boolean
      status?: 'active' | 'draft' | 'archived'
      customFields?: Record<string, any>
    } = {}
  ): Promise<ApiResponse<Product>> {
    try {
      const {
        titleSuffix = ' (Copy)',
        includeInventory = true,
        includeImages = true,
        includeVariants = true,
        includeCategories = true,
        includeCollections = true,
        status = PRODUCT_STATUSES.DRAFT,
        customFields = {}
      } = options

      // Get the original product
      const originalProduct = await this.getProductById(productId)
      if (!originalProduct.success || !originalProduct.data) {
        throw new NotFoundError('Product', productId)
      }

      const product = originalProduct.data

      // Create the duplicate
      const duplicateInput: CreateProductInput = {
        title: product.title + titleSuffix,
        description: product.description,
        descriptionHtml: product.descriptionHtml,
        vendor: product.vendor,
        productType: product.productType,
        status,
        price: product.price,
        compareAtPrice: product.compareAtPrice,
        costPerItem: product.costPerItem,
        trackQuantity: product.trackQuantity,
        continueSellingWhenOutOfStock: product.continueSellingWhenOutOfStock,
        inventoryQuantity: includeInventory ? product.inventoryQuantity : 0,
        weight: product.weight,
        weightUnit: product.weightUnit,
        dimensions: product.dimensions,
        seo: {
          title: product.seo?.title ? product.seo.title + titleSuffix : undefined,
          description: product.seo?.description,
          keywords: product.seo?.keywords
        },
        metafields: { ...product.metafields, ...customFields },
        isGiftCard: product.isGiftCard,
        requiresShipping: product.requiresShipping,
        isTaxable: product.isTaxable,
        isVisible: product.isVisible,
        images: includeImages ? product.images : undefined,
        variants: includeVariants ? product.variants?.map(variant => ({
          ...variant,
          sku: variant.sku + '-copy',
          title: variant.title,
          price: variant.price,
          compareAtPrice: variant.compareAtPrice,
          weight: variant.weight,
          weightUnit: variant.weightUnit,
          inventoryQuantity: includeInventory ? variant.inventoryQuantity : 0,
          inventoryPolicy: variant.inventoryPolicy,
          fulfillmentService: variant.fulfillmentService,
          inventoryManagement: variant.inventoryManagement,
          available: variant.available,
          options: variant.options,
        })) : undefined,
        options: product.options,
        categoryIds: includeCategories ? product.categories?.map(c => c.id) : undefined,
        collectionIds: includeCollections ? product.collections?.map(c => c.id) : undefined,
        tagIds: product.tags?.map(t => t.id)
      }

      return await this.createProduct(duplicateInput)
    } catch (error) {
      console.error('Error duplicating product:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to duplicate product'
        }
      }
    }
  }
}
