// Order Management Service
// Handles all order-related operations including CRUD, search, and analytics

import { PrismaClient } from '@prisma/client'
import type { 
  Order, 
  CreateOrderInput, 
  UpdateOrderInput, 
  OrderSearchParams,
  OrderFilters,
  OrderSortOptions,
  PaginatedResponse,
  ApiResponse,
  NotFoundError,
  ValidationError
} from '../types'
import { DEFAULT_CURRENCY, PAGINATION, ORDER_STATUSES, PAYMENT_STATUSES } from '../config/constants'

const prisma = new PrismaClient()

export class OrderService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Create a new order
   */
  async createOrder(input: CreateOrderInput): Promise<ApiResponse<Order>> {
    try {
      // Validate input
      this.validateOrderInput(input)

      // Generate order number
      const orderNumber = await this.generateOrderNumber()

      // Calculate totals
      const calculations = await this.calculateOrderTotals(input)

      // Check inventory availability
      await this.validateInventoryAvailability(input.items)

      // Create order with transaction
      const order = await this.db.$transaction(async (tx) => {
        // Create the order
        const newOrder = await tx.order.create({
          data: {
            orderNumber,
            userId: input.userId,
            customerEmail: input.customerEmail,
            customerFirstName: input.customerFirstName,
            customerLastName: input.customerLastName,
            customerPhone: input.customerPhone,
            status: ORDER_STATUSES.PENDING,
            paymentStatus: PAYMENT_STATUSES.PENDING,
            subtotal: calculations.subtotal,
            shippingCost: calculations.shippingCost,
            taxAmount: calculations.taxAmount,
            discountAmount: calculations.discountAmount,
            total: calculations.total,
            currency: input.currency || DEFAULT_CURRENCY,
            shippingAddress: input.shippingAddress as any,
            billingAddress: input.billingAddress as any,
            shippingMethod: input.shippingMethod,
            paymentMethod: input.paymentMethod,
            notes: input.notes,
            tags: input.tags || [],
            
            // Create order items
            items: {
              create: input.items.map((item, index) => ({
                productId: item.productId,
                variantId: item.variantId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.unitPrice * item.quantity,
                productTitle: item.productTitle,
                productSlug: item.productSlug,
                variantTitle: item.variantTitle,
                productImage: item.productImage,
                productSku: item.productSku,
                variantSku: item.variantSku,
                weight: item.weight,
                requiresShipping: item.requiresShipping ?? true,
                isTaxable: item.isTaxable ?? true,
                fulfillmentService: item.fulfillmentService || 'manual',
                grams: item.grams,
                vendor: item.vendor,
                properties: item.properties as any,
                giftCard: item.giftCard ?? false,
                position: index + 1
              }))
            }
          },
          include: {
            items: true,
            user: true,
            fulfillments: true,
            payments: true
          }
        })

        // Reserve inventory for each item
        for (const item of input.items) {
          await this.reserveInventory(tx, item.productId, item.variantId, item.quantity, newOrder.id)
        }

        return newOrder
      })

      return {
        success: true,
        data: this.transformOrderData(order)
      }
    } catch (error) {
      console.error('Error creating order:', error)
      return {
        success: false,
        error: {
          code: error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create order'
        }
      }
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(id: string): Promise<ApiResponse<Order>> {
    try {
      const order = await this.db.order.findUnique({
        where: { id },
        include: {
          items: true,
          user: true,
          fulfillments: {
            include: {
              items: true
            }
          },
          payments: true,
          discountApplications: {
            include: {
              discount: true
            }
          }
        }
      })

      if (!order) {
        throw new NotFoundError('Order', id)
      }

      return {
        success: true,
        data: this.transformOrderData(order)
      }
    } catch (error) {
      console.error('Error getting order:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get order'
        }
      }
    }
  }

  /**
   * Get order by order number
   */
  async getOrderByNumber(orderNumber: string): Promise<ApiResponse<Order>> {
    try {
      const order = await this.db.order.findUnique({
        where: { orderNumber },
        include: {
          items: true,
          user: true,
          fulfillments: {
            include: {
              items: true
            }
          },
          payments: true,
          discountApplications: {
            include: {
              discount: true
            }
          }
        }
      })

      if (!order) {
        throw new NotFoundError('Order', orderNumber)
      }

      return {
        success: true,
        data: this.transformOrderData(order)
      }
    } catch (error) {
      console.error('Error getting order by number:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get order'
        }
      }
    }
  }

  /**
   * Search and filter orders
   */
  async searchOrders(params: OrderSearchParams = {}): Promise<ApiResponse<PaginatedResponse<Order>>> {
    try {
      const {
        query,
        filters = {},
        sort = { field: 'createdAt', direction: 'desc' },
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = params

      // Build where clause
      const where = this.buildOrderWhereClause(query, filters)

      // Build order by clause
      const orderBy = this.buildOrderOrderBy(sort)

      // Calculate pagination
      const skip = (page - 1) * Math.min(limit, PAGINATION.MAX_LIMIT)
      const take = Math.min(limit, PAGINATION.MAX_LIMIT)

      // Execute queries
      const [orders, total] = await Promise.all([
        this.db.order.findMany({
          where,
          include: {
            items: true,
            user: true,
            fulfillments: true,
            payments: true
          },
          orderBy,
          skip,
          take
        }),
        this.db.order.count({ where })
      ])

      const totalPages = Math.ceil(total / take)

      return {
        success: true,
        data: {
          data: orders.map((order: any) => this.transformOrderData(order)),
          pagination: {
            page,
            limit: take,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      }
    } catch (error) {
      console.error('Error searching orders:', error)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to search orders'
        }
      }
    }
  }

  /**
   * Update an existing order
   */
  async updateOrder(input: UpdateOrderInput): Promise<ApiResponse<Order>> {
    try {
      // Check if order exists
      const existingOrder = await this.db.order.findUnique({
        where: { id: input.id }
      })

      if (!existingOrder) {
        throw new NotFoundError('Order', input.id)
      }

      // Update order
      const updatedOrder = await this.db.order.update({
        where: { id: input.id },
        data: {
          customerEmail: input.customerEmail,
          customerFirstName: input.customerFirstName,
          customerLastName: input.customerLastName,
          customerPhone: input.customerPhone,
          status: input.status,
          paymentStatus: input.paymentStatus,
          shippingAddress: input.shippingAddress as any,
          billingAddress: input.billingAddress as any,
          shippingMethod: input.shippingMethod,
          paymentMethod: input.paymentMethod,
          notes: input.notes,
          tags: input.tags,
          trackingNumber: input.trackingNumber,
          trackingUrl: input.trackingUrl,
          updatedAt: new Date()
        },
        include: {
          items: true,
          user: true,
          fulfillments: true,
          payments: true
        }
      })

      return {
        success: true,
        data: this.transformOrderData(updatedOrder)
      }
    } catch (error) {
      console.error('Error updating order:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' :
                error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update order'
        }
      }
    }
  }

  /**
   * Delete an order
   */
  async deleteOrder(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if order exists
      const existingOrder = await this.db.order.findUnique({
        where: { id },
        include: { items: true }
      })

      if (!existingOrder) {
        throw new NotFoundError('Order', id)
      }

      // Delete order in transaction (release inventory first)
      await this.db.$transaction(async (tx) => {
        // Release inventory reservations
        for (const item of existingOrder.items) {
          await this.releaseInventory(tx, item.productId, item.variantId, item.quantity, id)
        }

        // Delete order (cascade will handle related records)
        await tx.order.delete({
          where: { id }
        })
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Error deleting order:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete order'
        }
      }
    }
  }

  // Private helper methods
  private validateOrderInput(input: CreateOrderInput): void {
    if (!input.customerEmail?.trim()) {
      throw new ValidationError('Customer email is required')
    }

    if (!input.items || input.items.length === 0) {
      throw new ValidationError('Order must have at least one item')
    }

    if (!input.shippingAddress) {
      throw new ValidationError('Shipping address is required')
    }

    for (const item of input.items) {
      if (!item.productId) {
        throw new ValidationError('Product ID is required for all items')
      }
      if (item.quantity <= 0) {
        throw new ValidationError('Item quantity must be greater than 0')
      }
      if (item.unitPrice <= 0) {
        throw new ValidationError('Item unit price must be greater than 0')
      }
    }
  }

  private async generateOrderNumber(): Promise<string> {
    const prefix = 'ORD'
    const timestamp = Date.now().toString().slice(-8)
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()
    return `${prefix}-${timestamp}-${random}`
  }

  private async calculateOrderTotals(input: CreateOrderInput) {
    const subtotal = input.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0)

    // Calculate shipping cost (simplified - in production, use shipping service)
    const shippingCost = this.calculateShippingCost(input.shippingMethod, subtotal)

    // Calculate tax (simplified - in production, use tax service)
    const taxAmount = this.calculateTax(subtotal, input.shippingAddress)

    // Calculate discount (simplified - in production, use discount service)
    const discountAmount = input.discountCode ? await this.calculateDiscount(input.discountCode, subtotal) : 0

    const total = subtotal + shippingCost + taxAmount - discountAmount

    return {
      subtotal,
      shippingCost,
      taxAmount,
      discountAmount,
      total
    }
  }

  private calculateShippingCost(shippingMethod: string, subtotal: number): number {
    // Simplified shipping calculation - in production, integrate with shipping providers
    const shippingRates = {
      'standard': 50,
      'express': 100,
      'overnight': 200,
      'free': 0
    }

    // Free shipping over R500
    if (subtotal >= 500) {
      return 0
    }

    return shippingRates[shippingMethod as keyof typeof shippingRates] || 50
  }

  private calculateTax(subtotal: number, shippingAddress: any): number {
    // South African VAT is 15%
    const vatRate = 0.15
    return Math.round(subtotal * vatRate * 100) / 100
  }

  private async calculateDiscount(discountCode: string, subtotal: number): Promise<number> {
    // Simplified discount calculation - in production, use discount service
    try {
      const discount = await this.db.discount.findFirst({
        where: {
          code: discountCode,
          isActive: true,
          startsAt: { lte: new Date() },
          OR: [
            { endsAt: null },
            { endsAt: { gte: new Date() } }
          ]
        }
      })

      if (!discount) return 0

      if (discount.type === 'percentage') {
        return Math.round(subtotal * (discount.value / 100) * 100) / 100
      } else {
        return Math.min(discount.value, subtotal)
      }
    } catch (error) {
      console.error('Error calculating discount:', error)
      return 0
    }
  }

  private async validateInventoryAvailability(items: any[]): Promise<void> {
    for (const item of items) {
      const product = await this.db.product.findUnique({
        where: { id: item.productId },
        include: {
          variants: item.variantId ? {
            where: { id: item.variantId }
          } : undefined
        }
      })

      if (!product) {
        throw new ValidationError(`Product not found: ${item.productId}`)
      }

      const availableQuantity = item.variantId
        ? product.variants[0]?.inventoryQuantity || 0
        : product.inventoryQuantity

      if (availableQuantity < item.quantity) {
        throw new ValidationError(`Insufficient inventory for ${product.title}. Available: ${availableQuantity}, Requested: ${item.quantity}`)
      }
    }
  }

  private async reserveInventory(tx: any, productId: string, variantId: string | null, quantity: number, orderId: string): Promise<void> {
    // In production, implement proper inventory reservation system
    // For now, just reduce available quantity
    if (variantId) {
      await tx.productVariant.update({
        where: { id: variantId },
        data: {
          inventoryQuantity: {
            decrement: quantity
          }
        }
      })
    } else {
      await tx.product.update({
        where: { id: productId },
        data: {
          inventoryQuantity: {
            decrement: quantity
          }
        }
      })
    }
  }

  private async releaseInventory(tx: any, productId: string, variantId: string | null, quantity: number, orderId: string): Promise<void> {
    // Release inventory reservation
    if (variantId) {
      await tx.productVariant.update({
        where: { id: variantId },
        data: {
          inventoryQuantity: {
            increment: quantity
          }
        }
      })
    } else {
      await tx.product.update({
        where: { id: productId },
        data: {
          inventoryQuantity: {
            increment: quantity
          }
        }
      })
    }
  }

  private buildOrderWhereClause(query?: string, filters: OrderFilters = {}) {
    const where: any = {}

    // Text search
    if (query) {
      where.OR = [
        { orderNumber: { contains: query, mode: 'insensitive' } },
        { customerEmail: { contains: query, mode: 'insensitive' } },
        { customerFirstName: { contains: query, mode: 'insensitive' } },
        { customerLastName: { contains: query, mode: 'insensitive' } },
        { trackingNumber: { contains: query, mode: 'insensitive' } }
      ]
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status }
    }

    // Payment status filter
    if (filters.paymentStatus && filters.paymentStatus.length > 0) {
      where.paymentStatus = { in: filters.paymentStatus }
    }

    // Date range filter
    if (filters.dateRange) {
      where.createdAt = {}
      if (filters.dateRange.from) {
        where.createdAt.gte = filters.dateRange.from
      }
      if (filters.dateRange.to) {
        where.createdAt.lte = filters.dateRange.to
      }
    }

    // Amount range filter
    if (filters.amountRange) {
      where.total = {}
      if (filters.amountRange.min !== undefined) {
        where.total.gte = filters.amountRange.min
      }
      if (filters.amountRange.max !== undefined) {
        where.total.lte = filters.amountRange.max
      }
    }

    // Customer filter
    if (filters.customerId) {
      where.userId = filters.customerId
    }

    return where
  }

  private buildOrderOrderBy(sort: OrderSortOptions) {
    const { field, direction } = sort

    switch (field) {
      case 'orderNumber':
        return { orderNumber: direction }
      case 'customerEmail':
        return { customerEmail: direction }
      case 'status':
        return { status: direction }
      case 'paymentStatus':
        return { paymentStatus: direction }
      case 'total':
        return { total: direction }
      case 'updatedAt':
        return { updatedAt: direction }
      case 'createdAt':
      default:
        return { createdAt: direction }
    }
  }

  private transformOrderData(order: any): Order {
    return {
      id: order.id,
      orderNumber: order.orderNumber,
      userId: order.userId,
      customerEmail: order.customerEmail,
      customerFirstName: order.customerFirstName,
      customerLastName: order.customerLastName,
      customerPhone: order.customerPhone,
      status: order.status,
      paymentStatus: order.paymentStatus,
      subtotal: Number(order.subtotal),
      shippingCost: Number(order.shippingCost),
      taxAmount: Number(order.taxAmount),
      discountAmount: Number(order.discountAmount),
      total: Number(order.total),
      currency: order.currency,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      shippingMethod: order.shippingMethod,
      paymentMethod: order.paymentMethod,
      trackingNumber: order.trackingNumber,
      trackingUrl: order.trackingUrl,
      notes: order.notes,
      tags: order.tags || [],
      items: order.items?.map((item: any) => ({
        id: item.id,
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        productTitle: item.productTitle,
        productSlug: item.productSlug,
        variantTitle: item.variantTitle,
        productImage: item.productImage,
        productSku: item.productSku,
        variantSku: item.variantSku,
        weight: item.weight,
        requiresShipping: item.requiresShipping,
        isTaxable: item.isTaxable,
        fulfillmentService: item.fulfillmentService,
        grams: item.grams,
        vendor: item.vendor,
        properties: item.properties,
        giftCard: item.giftCard,
        position: item.position
      })) || [],
      fulfillments: order.fulfillments || [],
      payments: order.payments || [],
      itemCount: order.items?.length || 0,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    }
  }

  /**
   * Get orders for a specific customer
   */
  async getCustomerOrders(customerId: string, params: OrderSearchParams = {}): Promise<ApiResponse<PaginatedResponse<Order>>> {
    const filters = { ...params.filters, customerId }
    return this.searchOrders({ ...params, filters })
  }

  /**
   * Get order statistics
   */
  async getOrderStats(filters: OrderFilters = {}): Promise<ApiResponse<any>> {
    try {
      const where = this.buildOrderWhereClause(undefined, filters)

      const [
        totalOrders,
        totalRevenue,
        averageOrderValue,
        statusCounts,
        paymentStatusCounts
      ] = await Promise.all([
        this.db.order.count({ where }),
        this.db.order.aggregate({
          where: { ...where, paymentStatus: 'paid' },
          _sum: { total: true }
        }),
        this.db.order.aggregate({
          where,
          _avg: { total: true }
        }),
        this.db.order.groupBy({
          by: ['status'],
          where,
          _count: true
        }),
        this.db.order.groupBy({
          by: ['paymentStatus'],
          where,
          _count: true
        })
      ])

      return {
        success: true,
        data: {
          totalOrders,
          totalRevenue: Number(totalRevenue._sum.total || 0),
          averageOrderValue: Number(averageOrderValue._avg.total || 0),
          statusBreakdown: statusCounts.reduce((acc, item) => {
            acc[item.status] = item._count
            return acc
          }, {} as Record<string, number>),
          paymentStatusBreakdown: paymentStatusCounts.reduce((acc, item) => {
            acc[item.paymentStatus] = item._count
            return acc
          }, {} as Record<string, number>)
        }
      }
    } catch (error) {
      console.error('Error getting order stats:', error)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get order statistics'
        }
      }
    }
  }
}
