import { PrismaClient } from '@prisma/client'
import type {
  Order,
  CreateOrderInput,
  UpdateOrderInput,
  OrderSearchParams,
  OrderFilters,
  OrderSortOptions,
  PaginatedResponse,
  ApiResponse,
  NotFoundError,
  ValidationError
} from '../types'
import { DEFAULT_CURRENCY, PAGINA<PERSON>ON, ORDER_STATUSES } from '../config/constants'

const prisma = new PrismaClient()

export interface OrderItem {
  productId: string
  variantId?: string
  quantity: number
  price: number
  name: string
  image?: string
  color?: string
  size?: string
}

export interface ShippingAddress {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  postalCode: string
  country: string
  phone?: string
}

export interface CreateOrderRequest {
  customerId?: string
  customerEmail: string
  customerPhone?: string
  items: OrderItem[]
  shippingAddress: ShippingAddress
  billingAddress?: ShippingAddress
  shippingMethod: string
  paymentMethod: string
  couponCode?: string
  notes?: string
}

export interface Order {
  id: string
  orderNumber: string
  customerId?: string
  customerEmail: string
  customerPhone?: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'
  items: OrderItem[]
  subtotal: number
  shippingCost: number
  taxAmount: number
  discountAmount: number
  total: number
  currency: string
  shippingAddress: ShippingAddress
  billingAddress?: ShippingAddress
  shippingMethod: string
  paymentMethod: string
  trackingNumber?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export class OrderService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  async createOrder(request: CreateOrderRequest): Promise<Order> {
    try {
      // Generate order number
      const orderNumber = await this.generateOrderNumber()

      // Calculate totals
      const subtotal = request.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      const shippingCost = await this.calculateShippingCost(request.shippingMethod, request.shippingAddress)
      const taxAmount = this.calculateTax(subtotal)
      const discountAmount = request.couponCode ? await this.calculateDiscount(request.couponCode, subtotal) : 0
      const total = subtotal + shippingCost + taxAmount - discountAmount

      // Check inventory availability
      for (const item of request.items) {
        const available = await this.inventoryService.checkAvailability(item.productId, item.variantId, item.quantity)
        if (!available) {
          throw new Error(`Insufficient inventory for product: ${item.name}`)
        }
      }

      // Create order in database
      const order = await prisma.order.create({
        data: {
          orderNumber,
          customerId: request.customerId,
          customerEmail: request.customerEmail,
          customerPhone: request.customerPhone,
          status: 'pending',
          paymentStatus: 'pending',
          subtotal,
          shippingCost,
          taxAmount,
          discountAmount,
          total,
          currency: 'ZAR',
          shippingAddress: request.shippingAddress,
          billingAddress: request.billingAddress || request.shippingAddress,
          shippingMethod: request.shippingMethod,
          paymentMethod: request.paymentMethod,
          notes: request.notes,
          items: {
            create: request.items.map(item => ({
              productId: item.productId,
              variantId: item.variantId,
              quantity: item.quantity,
              price: item.price,
              name: item.name,
              image: item.image,
              color: item.color,
              size: item.size
            }))
          }
        },
        include: {
          items: true
        }
      })

      // Reserve inventory
      for (const item of request.items) {
        await this.inventoryService.reserveInventory(item.productId, item.variantId, item.quantity, order.id)
      }

      // Send order confirmation email
      await this.emailService.sendOrderConfirmation(order)

      return this.formatOrder(order)
    } catch (error) {
      console.error('Order creation error:', error)
      throw new Error('Failed to create order')
    }
  }

  async getOrder(orderId: string): Promise<Order | null> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { items: true }
      })

      return order ? this.formatOrder(order) : null
    } catch (error) {
      console.error('Get order error:', error)
      return null
    }
  }

  async getOrderByNumber(orderNumber: string): Promise<Order | null> {
    try {
      const order = await prisma.order.findUnique({
        where: { orderNumber },
        include: { items: true }
      })

      return order ? this.formatOrder(order) : null
    } catch (error) {
      console.error('Get order by number error:', error)
      return null
    }
  }

  async getCustomerOrders(customerId: string, limit = 10, offset = 0): Promise<Order[]> {
    try {
      const orders = await prisma.order.findMany({
        where: { customerId },
        include: { items: true },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      })

      return orders.map(order => this.formatOrder(order))
    } catch (error) {
      console.error('Get customer orders error:', error)
      return []
    }
  }

  async getAllOrders(filters?: {
    page?: number
    limit?: number
    status?: string
    paymentStatus?: string
    startDate?: Date
    endDate?: Date
  }): Promise<Order[]> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        paymentStatus,
        startDate,
        endDate
      } = filters || {}

      const whereClause: any = {}

      if (status) {
        whereClause.status = status
      }

      if (paymentStatus) {
        whereClause.paymentStatus = paymentStatus
      }

      if (startDate || endDate) {
        whereClause.createdAt = {}
        if (startDate) whereClause.createdAt.gte = startDate
        if (endDate) whereClause.createdAt.lte = endDate
      }

      const orders = await prisma.order.findMany({
        where: whereClause,
        include: { items: true },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: (page - 1) * limit
      })

      return orders.map(order => this.formatOrder(order))
    } catch (error) {
      console.error('Get all orders error:', error)
      return []
    }
  }

  async updateOrderStatus(orderId: string, status: Order['status'], trackingNumber?: string): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          status,
          trackingNumber,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      // Send status update email
      await this.emailService.sendOrderStatusUpdate(this.formatOrder(order))

      // If cancelled, release inventory
      if (status === 'cancelled') {
        for (const item of order.items) {
          await this.inventoryService.releaseReservation(item.productId, item.variantId, item.quantity, orderId)
        }
      }

      // If confirmed, commit inventory
      if (status === 'confirmed') {
        for (const item of order.items) {
          await this.inventoryService.commitReservation(item.productId, item.variantId, item.quantity, orderId)
        }
      }

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update order status error:', error)
      return null
    }
  }

  async updatePaymentStatus(orderId: string, paymentStatus: Order['paymentStatus']): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      // If payment successful, update order status to confirmed
      if (paymentStatus === 'paid') {
        await this.updateOrderStatus(orderId, 'confirmed')
      }

      // If payment failed, cancel order
      if (paymentStatus === 'failed') {
        await this.updateOrderStatus(orderId, 'cancelled')
      }

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update payment status error:', error)
      return null
    }
  }

  async updateOrder(orderId: string, updateData: any): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          items: true,
          user: true,
          fulfillments: true,
          payments: true
        }
      })

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update order error:', error)
      return null
    }
  }

  async updatePaymentStatus(orderId: string, paymentStatus: string): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update payment status error:', error)
      return null
    }
  }

  async addOrderTags(orderId: string, tags: string[]): Promise<Order | null> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: { tags: true }
      })

      if (!order) return null

      const currentTags = order.tags || []
      const newTags = [...new Set([...currentTags, ...tags])]

      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: {
          tags: newTags,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(updatedOrder)
    } catch (error) {
      console.error('Add order tags error:', error)
      return null
    }
  }

  async removeOrderTags(orderId: string, tags: string[]): Promise<Order | null> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: { tags: true }
      })

      if (!order) return null

      const currentTags = order.tags || []
      const filteredTags = currentTags.filter(tag => !tags.includes(tag))

      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: {
          tags: filteredTags,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(updatedOrder)
    } catch (error) {
      console.error('Remove order tags error:', error)
      return null
    }
  }

  async getOrdersByIds(orderIds: string[]): Promise<Order[]> {
    try {
      const orders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true }
      })

      return orders.map(order => this.formatOrder(order))
    } catch (error) {
      console.error('Get orders by IDs error:', error)
      return []
    }
  }

  async exportOrders(orders: Order[]): Promise<any> {
    try {
      // Format orders for export
      const exportData = orders.map(order => ({
        orderNumber: order.orderNumber,
        customerEmail: order.customerEmail,
        customerName: `${order.customerFirstName} ${order.customerLastName}`,
        status: order.status,
        paymentStatus: order.paymentStatus,
        total: order.total,
        currency: order.currency,
        itemCount: order.itemCount,
        createdAt: order.createdAt,
        items: order.items?.map(item => ({
          productTitle: item.productTitle,
          variantTitle: item.variantTitle,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice
        })) || []
      }))

      return {
        data: exportData,
        summary: {
          totalOrders: orders.length,
          totalValue: orders.reduce((sum, order) => sum + Number(order.total), 0),
          averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + Number(order.total), 0) / orders.length : 0
        }
      }
    } catch (error) {
      console.error('Export orders error:', error)
      throw new Error('Failed to export orders')
    }
  }

  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      // First release any inventory reservations
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { items: true }
      })

      if (order) {
        for (const item of order.items) {
          await this.inventoryService.releaseReservation(item.productId, item.variantId, item.quantity, orderId)
        }
      }

      // Delete the order (cascade will handle related records)
      await prisma.order.delete({
        where: { id: orderId }
      })

      return true
    } catch (error) {
      console.error('Delete order error:', error)
      return false
    }
  }



  async getOrderStats(startDate?: Date, endDate?: Date) {
    try {
      const whereClause = {
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        })
      }

      const [totalOrders, totalRevenue, pendingOrders, shippedOrders] = await Promise.all([
        prisma.order.count({ where: whereClause }),
        prisma.order.aggregate({
          where: { ...whereClause, paymentStatus: 'paid' },
          _sum: { total: true }
        }),
        prisma.order.count({ where: { ...whereClause, status: 'pending' } }),
        prisma.order.count({ where: { ...whereClause, status: 'shipped' } })
      ])

      return {
        totalOrders,
        totalRevenue: totalRevenue._sum.total || 0,
        pendingOrders,
        shippedOrders,
        averageOrderValue: totalOrders > 0 ? (totalRevenue._sum.total || 0) / totalOrders : 0
      }
    } catch (error) {
      console.error('Get order stats error:', error)
      return {
        totalOrders: 0,
        totalRevenue: 0,
        pendingOrders: 0,
        shippedOrders: 0,
        averageOrderValue: 0
      }
    }
  }

  async exportOrders(orders: Order[], options?: any): Promise<any> {
    try {
      const { format = 'csv', includeItems = true, includeCustomer = true, includeAddresses = true } = options || {}

      const exportData = orders.map(order => {
        const baseData = {
          orderNumber: order.orderNumber,
          status: order.status,
          paymentStatus: order.paymentStatus,
          total: order.total,
          currency: order.currency,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt
        }

        if (includeCustomer && order.customer) {
          Object.assign(baseData, {
            customerEmail: order.customer.email,
            customerFirstName: order.customer.firstName,
            customerLastName: order.customer.lastName,
            customerPhone: order.customer.phone
          })
        }

        if (includeAddresses) {
          Object.assign(baseData, {
            shippingAddress: JSON.stringify(order.shippingAddress),
            billingAddress: JSON.stringify(order.billingAddress)
          })
        }

        if (includeItems) {
          Object.assign(baseData, {
            itemCount: order.itemCount,
            items: JSON.stringify(order.items)
          })
        }

        return baseData
      })

      if (format === 'json') {
        return exportData
      }

      if (format === 'csv') {
        const headers = Object.keys(exportData[0] || {})
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n')
        return csvContent
      }

      // For XLSX format, you would need to implement Excel generation
      // This is a placeholder
      return exportData
    } catch (error) {
      console.error('Export orders error:', error)
      throw new Error('Failed to export orders')
    }
  }

  async createFulfillment(orderId: string, fulfillmentData: any): Promise<any> {
    try {
      const fulfillment = await prisma.orderFulfillment.create({
        data: {
          orderId,
          status: 'pending',
          trackingNumber: fulfillmentData.trackingNumber,
          trackingUrl: fulfillmentData.trackingUrl,
          carrier: fulfillmentData.carrier,
          service: fulfillmentData.service,
          estimatedDelivery: fulfillmentData.estimatedDelivery,
          notes: fulfillmentData.notes,
          items: {
            create: fulfillmentData.items.map((item: any) => ({
              orderItemId: item.orderItemId,
              quantity: item.quantity
            }))
          }
        },
        include: {
          items: true
        }
      })

      return fulfillment
    } catch (error) {
      console.error('Create fulfillment error:', error)
      throw new Error('Failed to create fulfillment')
    }
  }

  async checkIfOrderFullyFulfilled(orderId: string): Promise<boolean> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: true,
          fulfillments: {
            include: {
              items: true
            }
          }
        }
      })

      if (!order) return false

      // Check if all order items have been fulfilled
      for (const orderItem of order.items) {
        const fulfilledQuantity = order.fulfillments.reduce((total, fulfillment) => {
          const fulfillmentItem = fulfillment.items.find(item => item.orderItemId === orderItem.id)
          return total + (fulfillmentItem?.quantity || 0)
        }, 0)

        if (fulfilledQuantity < orderItem.quantity) {
          return false
        }
      }

      return true
    } catch (error) {
      console.error('Check if order fully fulfilled error:', error)
      return false
    }
  }

  async sendFulfillmentNotification(orderId: string, fulfillmentId: string): Promise<void> {
    try {
      // This would integrate with email service to send fulfillment notification
      console.log(`Sending fulfillment notification for order ${orderId}, fulfillment ${fulfillmentId}`)
      // await this.emailService.sendFulfillmentNotification(orderId, fulfillmentId)
    } catch (error) {
      console.error('Send fulfillment notification error:', error)
    }
  }

  async getOrderFulfillments(orderId: string): Promise<any[]> {
    try {
      const fulfillments = await prisma.orderFulfillment.findMany({
        where: { orderId },
        include: {
          items: true
        },
        orderBy: { createdAt: 'desc' }
      })

      return fulfillments
    } catch (error) {
      console.error('Get order fulfillments error:', error)
      return []
    }
  }

  async updateFulfillment(fulfillmentId: string, updateData: any): Promise<any | null> {
    try {
      const fulfillment = await prisma.orderFulfillment.update({
        where: { id: fulfillmentId },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          items: true
        }
      })

      return fulfillment
    } catch (error) {
      console.error('Update fulfillment error:', error)
      return null
    }
  }

  async checkIfAllFulfillmentsDelivered(orderId: string): Promise<boolean> {
    try {
      const fulfillments = await prisma.orderFulfillment.findMany({
        where: { orderId }
      })

      if (fulfillments.length === 0) return false

      return fulfillments.every(fulfillment => fulfillment.status === 'delivered')
    } catch (error) {
      console.error('Check if all fulfillments delivered error:', error)
      return false
    }
  }

  async sendFulfillmentUpdateNotification(orderId: string, fulfillmentId: string): Promise<void> {
    try {
      // This would integrate with email service to send fulfillment update notification
      console.log(`Sending fulfillment update notification for order ${orderId}, fulfillment ${fulfillmentId}`)
      // await this.emailService.sendFulfillmentUpdateNotification(orderId, fulfillmentId)
    } catch (error) {
      console.error('Send fulfillment update notification error:', error)
    }
  }

  private async generateOrderNumber(): Promise<string> {
    const today = new Date()
    const year = today.getFullYear().toString().slice(-2)
    const month = (today.getMonth() + 1).toString().padStart(2, '0')
    const day = today.getDate().toString().padStart(2, '0')

    const prefix = `ORD${year}${month}${day}`

    // Get the count of orders created today
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const todayOrderCount = await prisma.order.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    })

    const sequence = (todayOrderCount + 1).toString().padStart(4, '0')
    return `${prefix}${sequence}`
  }

  private async calculateShippingCost(method: string, address: ShippingAddress): Promise<number> {
    // Implement shipping cost calculation based on method and address
    // This would integrate with courier services
    switch (method) {
      case 'standard':
        return 99.00
      case 'express':
        return 149.00
      case 'overnight':
        return 199.00
      case 'free':
        return 0.00
      default:
        return 99.00
    }
  }

  private calculateTax(subtotal: number): number {
    // South African VAT is 15%
    return subtotal * 0.15
  }

  private async calculateDiscount(couponCode: string, subtotal: number): Promise<number> {
    // Implement coupon/discount calculation
    // This would check against a coupons table
    return 0
  }

  private formatOrder(order: any): Order {
    return {
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      customerEmail: order.customerEmail,
      customerPhone: order.customerPhone,
      status: order.status,
      paymentStatus: order.paymentStatus,
      items: order.items,
      subtotal: order.subtotal,
      shippingCost: order.shippingCost,
      taxAmount: order.taxAmount,
      discountAmount: order.discountAmount,
      total: order.total,
      currency: order.currency,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      shippingMethod: order.shippingMethod,
      paymentMethod: order.paymentMethod,
      trackingNumber: order.trackingNumber,
      notes: order.notes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    }
  }
}
